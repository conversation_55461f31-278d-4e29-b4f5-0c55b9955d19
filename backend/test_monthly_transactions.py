#!/usr/bin/env python3
"""
Simple integration test for the monthly transactions endpoint
Run this script to test the /api/hsa/documents/monthly endpoint
"""

import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8088"
TEST_ENDPOINT = f"{BASE_URL}/api/hsa/documents/monthly"

def test_monthly_transactions_endpoint():
    """
    Test the monthly transactions endpoint
    Note: This requires a valid Supabase JWT token for testing
    """
    print("Testing Monthly Transactions Endpoint")
    print("=" * 50)
    
    # Test without authentication - should return 401
    print("\n1. Testing without authentication...")
    response = requests.get(TEST_ENDPOINT)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    assert response.status_code == 401, "Should return 401 without auth"
    print("✅ Correctly returns 401 without authentication")
    
    # Test with invalid token - should return 401
    print("\n2. Testing with invalid token...")
    headers = {"Authorization": "Bearer invalid_token"}
    response = requests.get(TEST_ENDPOINT, headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    assert response.status_code == 401, "Should return 401 with invalid token"
    print("✅ Correctly returns 401 with invalid token")
    
    print("\n3. Testing with valid token...")
    print("Note: You need to manually test this with a valid JWT token from login")
    print("Replace 'your_valid_token_here' with an actual token from the app")
    
    # Example of how to test with valid token (requires manual token)
    valid_token = "your_valid_token_here"  # Replace with actual token
    if valid_token != "your_valid_token_here":
        headers = {"Authorization": f"Bearer {valid_token}"}
        response = requests.get(TEST_ENDPOINT, headers=headers)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success! Found {data.get('total_count', 0)} transactions")
            print(f"Month: {data.get('month')}")
            print("✅ Monthly transactions endpoint working correctly")
        else:
            print(f"Error: {response.json()}")
    
    print("\n" + "=" * 50)
    print("Test completed successfully!")

def test_supabase_connection():
    """Test if the backend server is running"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Backend server status: {response.status_code}")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running!")
        return False

if __name__ == "__main__":
    print("Monthly Transactions Integration Test")
    print("=====================================")
    
    # Check if backend is running
    if test_supabase_connection():
        test_monthly_transactions_endpoint()
    else:
        print("Please start the backend server first:")
        print("source backend/venv/bin/activate && python3 backend/app.py")
