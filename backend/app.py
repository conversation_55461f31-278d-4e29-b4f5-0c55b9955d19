from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, create_access_token, jwt_required, get_jwt_identity
from dotenv import load_dotenv
import os
import json
import uuid
from functools import wraps
from supabase_client import SupabaseClient
from datetime import timedelta
import requests
from gemini_processor import GeminiProcessor
import time

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Configure CORS to allow requests from your frontend
CORS(app)

# JWT Configuration
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'dev-secret-key')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
jwt = JWTManager(app)

# Initialize Supabase client
supabase = SupabaseClient()

try:
    gemini_processor = GeminiProcessor()
    print("✅ Gemini processor initialized successfully")
except Exception as e:
    print(f"⚠️ Gemini processor initialization failed: {e}")
    print("Will use pytesseract OCR as fallback only")
    gemini_processor = None

# Custom authentication decorator for Supabase JWT
def supabase_auth_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        print(f"Auth header received: {auth_header[:50] if auth_header else 'None'}...")
        
        if not auth_header:
            print("No authorization header found")
            return jsonify({'error': 'Authorization header is required'}), 401
        
        try:
            # Extract token from "Bearer <token>"
            token = auth_header.split(' ')[1] if auth_header.startswith('Bearer ') else auth_header
            print(f"Extracted token length: {len(token)}")
            
            # Verify token with Supabase
            print("Verifying token with Supabase...")
            user_response = supabase.get_user(token)
            print(f"Supabase user response: {user_response}")
            
            if 'error' in user_response:
                print(f"Token validation failed: {user_response['error']}")
                # For the profile endpoint, we'll use a fallback mechanism
                if request.path == '/api/profile' and request.method == 'GET':
                    # Try to extract user ID from JWT without verification
                    try:
                        import jwt
                        decoded = jwt.decode(token, options={"verify_signature": False})
                        user_id = decoded.get("sub")
                        if user_id:
                            print(f"Using fallback authentication for profile endpoint with user ID: {user_id}")
                            request.current_user = {"id": user_id}
                            request.current_user_id = user_id
                            request.auth_token = token
                            return f(*args, **kwargs)
                    except Exception as jwt_error:
                        print(f"JWT decode error: {jwt_error}")
                
                return jsonify({'error': 'Invalid or expired token'}), 401
            
            # Add user info to request context
            request.current_user = user_response.get('user', {})
            request.current_user_id = request.current_user.get('id')
            request.auth_token = token
            
            print(f"Authentication successful for user: {request.current_user_id}")
            return f(*args, **kwargs)
        except Exception as e:
            print(f"Auth error: {str(e)}")
            return jsonify({'error': 'Authentication failed'}), 401
    
    return decorated_function


@app.route('/')
def hello_world():
    return 'Benefit Vault API is running!'


@app.route('/api/auth/signup', methods=['POST'])
def signup():
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        name = data.get('name')  # optional extra field

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        # Create user in Supabase with service role (bypasses email verification)
        print(f"Attempting to sign up with email: {email}")
        response = supabase.sign_up(email, password)
        print(f"Supabase signup response: {json.dumps(response, indent=2)}")
        
        # Check for error from Supabase
        if 'error' in response:
            error_msg = response['error']
            print(f"Supabase signup failed: {error_msg}")
            return jsonify({'error': error_msg}), 400

        # Get user data and access token
        user_id = response.get('id')
        access_token = response.get('access_token')
        
        if not user_id or not access_token:
            return jsonify({'error': 'Failed to create user account'}), 400

        # Prepare profile payload
        profile_data = {
            'id': user_id,  # Use the user ID as the profile ID
            'email': email,
            'full_name': name if name else None,
            'created_at': response.get('created_at')
        }

        # Create profile using the service role
        try:
            profile_response = supabase.execute_query('profiles', profile_data, 'insert')
            print(f"Profile created: {json.dumps(profile_response, indent=2)}")
        except Exception as e:
            print(f"Create profile error: {str(e)}")

        # Generate our own JWT for the frontend
        app_access_token = create_access_token(identity=user_id)

        return jsonify({
            'message': 'User created successfully',
            'user': {
                'id': user_id,
                'email': email,
                'access_token': access_token  # Supabase token
            },
            'access_token': app_access_token  # Our app token
        }), 201

    except Exception as e:
        print(f"Signup error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        # Get user data from request
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        # Validate input
        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400
        
        # Authenticate with Supabase Auth
        print(f"Attempting login for {email}")
        auth_response = supabase.sign_in(email, password)
        print(f"Supabase login response: {json.dumps(auth_response, indent=2)}")
        
        # Check for authentication errors
        if 'error' in auth_response:
            error_msg = auth_response.get('error_description') or auth_response.get('error') or 'Invalid credentials'
            print(f"Login failed with error: {error_msg}")
            
            # If email not confirmed, try to auto-confirm by signing up again
            if error_msg == 'Email not confirmed':
                print("Attempting to auto-confirm email...")
                signup_response = supabase.sign_up(email, password)
                if 'error' not in signup_response:
                    # Try login again
                    auth_response = supabase.sign_in(email, password)
                    if 'error' not in auth_response:
                        print("Auto-confirmation successful")
                    else:
                        return jsonify({'error': 'Failed to auto-confirm email'}), 401
                else:
                    return jsonify({'error': signup_response['error']}), 401
            else:
                return jsonify({'error': error_msg}), 401
        
        # Get user data from response
        user = auth_response.get('user', {})
        user_id = user.get('id')
        supabase_access_token = auth_response.get('access_token')
        supabase_refresh_token = auth_response.get('refresh_token')
        expires_in = auth_response.get('expires_in', 3600)  # Default 1 hour
        token_type = auth_response.get('token_type', 'bearer')
        
        if not user_id or not supabase_access_token:
            return jsonify({'error': 'Invalid response from authentication service'}), 401
        
        # Calculate expires_at timestamp
        expires_at = int(time.time()) + expires_in
        
        # Get user profile data
        profile_response = supabase.get_profile(user_id, supabase_access_token)
        
        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': user_id,
                'email': email,
                'profile': profile_response[0] if profile_response else None
            },
            'access_token': supabase_access_token,
            'refresh_token': supabase_refresh_token,
            'expires_in': expires_in,
            'expires_at': expires_at,
            'token_type': token_type
        }), 200
            
    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@app.route('/api/auth/refresh', methods=['POST'])
def refresh_token():
    try:
        print("🔄 Refresh token endpoint called")
        data = request.get_json()
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            print("❌ Refresh token is missing in request")
            return jsonify({'error': 'Refresh token is required'}), 400
        
        # Log partial token for debugging (first and last 5 chars)
        token_preview = f"{refresh_token[:5]}...{refresh_token[-5:]}" if len(refresh_token) > 10 else "[invalid token]"
        print(f"🔄 Attempting to refresh with token: {token_preview}")
        
        # Call Supabase client to refresh token
        refresh_response = supabase.refresh_token(refresh_token)
        
        # Log the response for debugging
        print(f"🔄 Supabase refresh response: {refresh_response}")
        
        if 'error' in refresh_response:
            error_msg = refresh_response['error']
            print(f"❌ Token refresh failed: {error_msg}")
            return jsonify({'error': error_msg}), 401
        
        # Get tokens from response
        access_token = refresh_response.get('access_token')
        new_refresh_token = refresh_response.get('refresh_token')
        user_data = refresh_response.get('user', {})
        expires_in = refresh_response.get('expires_in', 3600)  # Default 1 hour
        token_type = refresh_response.get('token_type', 'bearer')
        
        if not access_token or not new_refresh_token:
            print("❌ Invalid response from Supabase: missing tokens")
            return jsonify({'error': 'Invalid response from authentication server'}), 500
        
        # Calculate expires_at timestamp
        expires_at = int(time.time()) + expires_in
        
        # Log success
        print(f"✅ Token refresh successful. New access token: {access_token[:5]}...")
        print(f"Token refresh successful for user: {user_data.get('id', 'unknown')}")
        
        return jsonify({
            'message': 'Token refreshed successfully',
            'access_token': access_token,
            'refresh_token': new_refresh_token,
            'expires_in': expires_in,
            'expires_at': expires_at,
            'token_type': token_type,
            'user': user_data
        }), 200
    except Exception as e:
        print(f"❌ Token refresh error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@app.route('/api/profile', methods=['GET'])
@supabase_auth_required
def get_profile():
    try:
        # Get current user ID from JWT token
        current_user_id = request.current_user_id
        
        # Get user profile from Supabase
        response = supabase.get_profile(current_user_id)
        
        if response and len(response) > 0:
            return jsonify(response[0]), 200
        else:
            return jsonify({'error': 'Profile not found'}), 404
            
    except Exception as e:
        print(f"Get profile error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/profile', methods=['PUT'])
@supabase_auth_required
def update_profile():
    try:
        # Get current user ID from JWT token
        current_user_id = request.current_user_id
        
        # Get profile data from request
        profile_data = request.json
        
        # Validate required fields
        if not profile_data:
            return jsonify({'error': 'No profile data provided'}), 400
            
        # Update user profile in Supabase
        response = supabase.update_profile(current_user_id, profile_data)
        
        return jsonify({'message': 'Profile updated successfully', 'data': response}), 200
            
    except Exception as e:
        print(f"Update profile error: {str(e)}")
        return jsonify({'error': str(e)}), 500


# HSA Document Management Endpoints
@app.route('/api/hsa/documents', methods=['POST'])
@supabase_auth_required
def upload_hsa_document():
    try:
        current_user_id = request.current_user_id
        
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Read file data once
        file_data = file.read()
        file_size = len(file_data)
        
        # Process image with OCR to extract document fields
        print("Processing image with OCR...")
        if gemini_processor:
            ocr_result = gemini_processor.process_receipt_image(file_data)
            if not ocr_result['success']:
                print("Gemini OCR failed, falling back to pytesseract...")
                ocr_result = ocr_processor.process_image(file_data)
        else:
            ocr_result = ocr_processor.process_image(file_data)
        
        # Get form data (manual input) - these will override OCR if provided
        manual_title = request.form.get('title', '').strip()
        manual_description = request.form.get('description', '').strip()
        manual_amount = request.form.get('amount', '').strip()
        manual_category = request.form.get('category', '').strip()
        manual_expense_date = request.form.get('expense_date', '').strip()
        
        # Use OCR extracted data as defaults, override with manual input if provided
        if ocr_result['success']:
            extracted_data = ocr_result['extracted_data']
            title = manual_title if manual_title else extracted_data.get('title', 'Medical Expense')
            description = manual_description if manual_description else extracted_data.get('description', 'Medical expense from receipt')
            amount = manual_amount if manual_amount else str(extracted_data.get('amount', 0))
            category = manual_category if manual_category else extracted_data.get('category', 'Medical')
            expense_date = manual_expense_date if manual_expense_date else extracted_data.get('expense_date', '')
        else:
            # Fallback to manual input or defaults if OCR fails
            title = manual_title if manual_title else 'Medical Expense'
            description = manual_description if manual_description else 'Medical expense from receipt'
            amount = manual_amount if manual_amount else '0'
            category = manual_category if manual_category else 'Medical'
            expense_date = manual_expense_date
        
        # Validate required fields
        if not title:
            return jsonify({'error': 'Title is required'}), 400
        
        # Generate unique file path
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'bin'
        unique_filename = f"{str(uuid.uuid4())}.{file_extension}"
        file_path = f"{current_user_id}/{unique_filename}"
        
        # Upload file to Supabase Storage
        file_result = supabase.upload_file_to_storage(
            file_data=file_data,
            file_path=file_path,
            auth_token=request.auth_token
        )
        
        # Check if upload was successful
        if 'error' in file_result:
            return jsonify({'error': f'Failed to upload file: {file_result.get("error", "Unknown error")}'}), 500
        
        # Get file URL
        file_url = supabase.get_file_url(file_path)
        
        # Prepare document data
        document_data = {
            'title': title,
            'description': description,
            'amount': float(amount) if amount else 0,
            'category': category,
            'expense_date': expense_date if expense_date else None,
            'file_path': file_path,
            'file_url': file_url,
            'file_name': file.filename,
            'file_size': file_size
        }
        
        # Upload document metadata
        result = supabase.upload_hsa_document(current_user_id, document_data)
        
        # Include OCR results in response for frontend
        response_data = {
            'message': 'Document uploaded successfully',
            'document': result,
            'ocr_result': {
                'success': ocr_result['success'],
                'extracted_data': ocr_result.get('extracted_data', {}),
                'confidence': ocr_result.get('confidence', 'low'),
                'raw_text': ocr_result.get('raw_text', '') if ocr_result['success'] else None
            }
        }
        
        return jsonify(response_data), 201
        
    except Exception as e:
        print(f"Upload document error: {str(e)}")
        return jsonify({'error': f'Failed to upload document: {str(e)}'}), 500


@app.route('/api/hsa/documents', methods=['GET'])
@supabase_auth_required
def get_hsa_documents():
    try:
        current_user_id = request.current_user_id
        
        # Get documents from Supabase
        documents = supabase.get_hsa_documents(current_user_id)
        
        return jsonify({
            'documents': documents
        }), 200
        
    except Exception as e:
        print(f"Get documents error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/hsa/documents/monthly', methods=['GET'])
@supabase_auth_required
def get_monthly_hsa_documents():
    try:
        from datetime import datetime, timedelta
        current_user_id = request.current_user_id
        
        # Get current year and month
        now = datetime.now()
        current_year = now.year
        current_month = now.month
        
        # Get documents from Supabase for current month
        documents = supabase.get_monthly_hsa_documents(current_user_id, current_year, current_month)
        
        return jsonify({
            'documents': documents,
            'month': f"{current_year}-{current_month:02d}",
            'total_count': len(documents)
        }), 200
        
    except Exception as e:
        print(f"Get monthly documents error: {str(e)}")
        return jsonify({'error': 'Failed to get monthly HSA documents'}), 500


@app.route('/api/hsa/documents/<int:document_id>', methods=['GET'])
@supabase_auth_required
def get_hsa_document(document_id):
    try:
        current_user_id = request.current_user_id
        
        # Get specific document
        document = supabase.get_hsa_document(document_id, current_user_id)
        
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        
        return jsonify({
            'document': document[0] if document else None
        }), 200
        
    except Exception as e:
        print(f"Get document error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/hsa/documents/<int:document_id>', methods=['PUT'])
@supabase_auth_required
def update_hsa_document(document_id):
    try:
        current_user_id = request.current_user_id
        
        # Get update data
        update_data = request.get_json()
        
        # Update document
        result = supabase.update_hsa_document(document_id, current_user_id, update_data)
        
        return jsonify({
            'message': 'Document updated successfully',
            'document': result
        }), 200
        
    except Exception as e:
        print(f"Update document error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/hsa/documents/<int:document_id>', methods=['DELETE'])
@supabase_auth_required
def delete_hsa_document(document_id):
    try:
        current_user_id = request.current_user_id
        
        # Get document first to get file path
        document = supabase.get_hsa_document(document_id, current_user_id)
        
        if not document:
            return jsonify({'error': 'Document not found'}), 404
        
        # Delete file from storage
        if document[0].get('file_path'):
            supabase.delete_file_from_storage('hsa-documents', document[0]['file_path'])
        
        # Delete document metadata
        result = supabase.delete_hsa_document(document_id, current_user_id)
        
        return jsonify({
            'message': 'Document deleted successfully'
        }), 200
        
    except Exception as e:
        print(f"Delete document error: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/hsa/summary', methods=['GET'])
@supabase_auth_required
def get_hsa_summary():
    try:
        user_id = request.current_user_id
        print(f"Getting HSA summary for user: {user_id}")
        
        # Optional 'year' query parameter
        year_param = request.args.get('year', type=int)
        summary = supabase.get_hsa_summary(user_id, year_param)
        return jsonify(summary)
    except Exception as e:
        print(f"Error getting HSA summary: {str(e)}")
        return jsonify({'error': 'Failed to get HSA summary'}), 500


# Auth Management Endpoints
@app.route('/api/auth/change-password', methods=['POST'])
@supabase_auth_required
def change_password():
    try:
        # Get current user ID and auth token from request context
        current_user_id = request.current_user_id
        auth_token = request.auth_token
        
        # Get password data from request
        data = request.get_json()
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        
        # Validate input
        if not current_password or not new_password:
            return jsonify({
                'error': 'Current password and new password are required'
            }), 400
            
        # Validate password strength
        if len(new_password) < 8:
            return jsonify({
                'error': 'New password must be at least 8 characters long'
            }), 400
            
        # Attempt to change password
        result = supabase.change_password(current_password, new_password, auth_token)
        
        if 'error' in result:
            return jsonify({
                'error': result['error']
            }), 400
        
        return jsonify({
            'message': 'Password changed successfully'
        }), 200
        
    except Exception as e:
        print(f"Change password error: {str(e)}")
        return jsonify({'error': str(e)}), 500


# HSA Contributions Management Endpoints
@app.route('/api/hsa/contributions', methods=['POST'])
@supabase_auth_required
def add_hsa_contribution():
    try:
        user_id = request.current_user_id
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        amount = data.get('amount')
        contribution_date = data.get('contribution_date')
        description = data.get('description')
        
        if not amount:
            return jsonify({'error': 'Amount is required'}), 400
        
        try:
            amount = float(amount)
            if amount <= 0:
                return jsonify({'error': 'Amount must be greater than 0'}), 400
        except ValueError:
            return jsonify({'error': 'Invalid amount format'}), 400
        
        print(f"Adding HSA contribution for user: {user_id}, amount: ${amount}")
        
        result = supabase.add_hsa_contribution(
            user_id=user_id,
            amount=amount,
            contribution_date=contribution_date,
            description=description
        )
        
        if 'error' in result:
            return jsonify({'error': result['error']}), 500
        
        return jsonify(result), 201
        
    except Exception as e:
        print(f"Error adding HSA contribution: {str(e)}")
        return jsonify({'error': 'Failed to add contribution'}), 500

@app.route('/api/hsa/contributions', methods=['GET'])
@supabase_auth_required
def get_hsa_contributions():
    try:
        user_id = request.current_user_id
        year = request.args.get('year', type=int)
        
        print(f"Getting HSA contributions for user: {user_id}, year: {year}")
        
        contributions = supabase.get_hsa_contributions(user_id, year)
        return jsonify(contributions)
        
    except Exception as e:
        print(f"Error getting HSA contributions: {str(e)}")
        return jsonify({'error': 'Failed to get contributions'}), 500

@app.route('/api/hsa/contributions/<contribution_id>', methods=['DELETE', 'GET'])
@supabase_auth_required
def handle_contribution(contribution_id):
    if request.method == 'DELETE':
        try:
            user_id = request.current_user_id
            
            print(f"Deleting HSA contribution: {contribution_id} for user: {user_id}")
            
            success = supabase.delete_hsa_contribution(contribution_id, user_id)
            
            if success:
                return jsonify({'message': 'Contribution deleted successfully'}), 200
            else:
                return jsonify({'error': 'Failed to delete contribution'}), 500
                
        except Exception as e:
            print(f"Error deleting HSA contribution: {str(e)}")
            return jsonify({'error': 'Failed to delete contribution'}), 500
    elif request.method == 'GET':
        try:
            user_id = request.current_user_id
            
            print(f"Getting HSA contribution: {contribution_id} for user: {user_id}")
            
            contribution = supabase.get_hsa_contribution_by_id(contribution_id, user_id)
            
            if contribution:
                return jsonify(contribution), 200
            else:
                return jsonify({'error': 'Contribution not found'}), 404
                
        except Exception as e:
            print(f"Error getting HSA contribution: {str(e)}")
            return jsonify({'error': 'Failed to get contribution'}), 500

@app.route('/api/hsa/contributions/total', methods=['GET'])
@supabase_auth_required
def get_yearly_contribution_total():
    try:
        user_id = request.current_user_id
        year = request.args.get('year', type=int)
        
        print(f"Getting yearly contribution total for user: {user_id}, year: {year}")
        
        total = supabase.get_yearly_contribution_total(user_id, year)
        return jsonify({'total': total, 'year': year})
        
    except Exception as e:
        print(f"Error getting yearly contribution total: {str(e)}")
        return jsonify({'error': 'Failed to get contribution total'}), 500

@app.route('/api/hsa/transactions/recent', methods=['GET'])
@supabase_auth_required
def get_recent_transactions():
    try:
        # Get user ID directly from the authenticated request context
        current_user_id = request.current_user_id
        print(f"Getting recent transactions for user ID: {current_user_id}")

        # Get recent transactions
        result = supabase.get_recent_transactions(current_user_id)
        
        if 'error' in result:
            return jsonify({'error': result['error']}), 400
            
        return jsonify(result), 200

    except Exception as e:
        print(f"Error in get_recent_transactions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/hsa/contributions/current-year', methods=['GET'])
@supabase_auth_required
def get_current_year_contributions():
    try:
        user_id = request.current_user_id
        contributions = supabase.get_current_year_contributions(user_id)
        
        if isinstance(contributions, list):
            return jsonify(contributions)
        else:
            return jsonify([])
    except Exception as e:
        print(f"Error getting current year contributions: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/hsa/monthly-chart-data', methods=['GET'])
@supabase_auth_required
def get_monthly_chart_data():
    """Get monthly expenses and contributions data for the past 12 months"""
    try:
        user_id = request.current_user_id
        print(f"Getting monthly chart data for user: {user_id}")
        
        from datetime import datetime, timedelta
        import calendar
        
        # Calculate date range for the last 12 months
        current_date = datetime.now()
        start_date = current_date - timedelta(days=365)  # Approximately 12 months ago
        
        # Get all HSA documents for this user
        documents_endpoint = f"{supabase.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&select=*"
        documents_response = requests.get(documents_endpoint, headers=supabase.service_headers)
        
        if documents_response.status_code != 200:
            print(f"Error getting HSA documents: {documents_response.text}")
            return jsonify([])
        
        documents = documents_response.json()
        print(f"Found {len(documents)} HSA documents")
        
        # Get all HSA contributions for this user using the existing method
        print(f"Fetching contributions for user: {user_id}")
        # Use the existing method that's known to work for other parts of the app
        contributions = supabase.get_hsa_contributions(user_id)
        print(f"Found {len(contributions)} HSA contributions")
        # Debug: Print first few contributions
        for i, contrib in enumerate(contributions[:3]):
            print(f"Contribution {i}: {contrib}")
        
        # Process expenses by month
        expenses_by_month = {}
        for doc in documents:
            if doc.get('expense_date') and doc.get('amount'):
                try:
                    expense_date = datetime.strptime(doc['expense_date'], '%Y-%m-%d')
                    if expense_date >= start_date:
                        month_key = expense_date.strftime('%Y-%m')
                        if month_key not in expenses_by_month:
                            expenses_by_month[month_key] = 0
                        expenses_by_month[month_key] += float(doc['amount'])
                except (ValueError, TypeError) as e:
                    print(f"Error parsing expense date {doc.get('expense_date')}: {e}")
                    continue
        
        # Process contributions by month
        contributions_by_month = {}
        print("Processing contributions by month...")
        for contrib in contributions:
            if contrib.get('contribution_date') and contrib.get('amount'):
                try:
                    print(f"Processing contribution: date={contrib['contribution_date']}, amount={contrib['amount']}")
                    contrib_date = datetime.strptime(contrib['contribution_date'], '%Y-%m-%d')
                    if contrib_date >= start_date:
                        month_key = contrib_date.strftime('%Y-%m')
                        if month_key not in contributions_by_month:
                            contributions_by_month[month_key] = 0
                        contributions_by_month[month_key] += float(contrib['amount'])
                        print(f"Added contribution to month {month_key}: ${float(contrib['amount'])}")
                except (ValueError, TypeError) as e:
                    print(f"Error parsing contribution date {contrib.get('contribution_date')}: {e}")
                    continue
        
        # Debug: Print all months with contributions
        print("Months with contributions:")
        for month, amount in contributions_by_month.items():
            print(f"  {month}: ${amount}")
        
        # Generate the last 12 months of data
        monthly_data = []
        print("Generating monthly data...")
        for i in range(11, -1, -1):  # Last 12 months
            month_date = current_date - timedelta(days=30 * i)
            month_key = month_date.strftime('%Y-%m')
            month_name = calendar.month_abbr[month_date.month]
            
            expenses = expenses_by_month.get(month_key, 0)
            contributions = contributions_by_month.get(month_key, 0)
            
            print(f"Month {month_name} ({month_key}): expenses=${expenses}, contributions=${contributions}")
            
            monthly_data.append({
                'month': month_name,
                'month_key': month_key,
                'expenses': expenses,
                'contributions': contributions
            })
        
        print(f"Generated monthly data for {len(monthly_data)} months")
        return jsonify(monthly_data)
        
    except Exception as e:
        print(f"Error in get_monthly_chart_data: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/hsa/weekly-chart-data', methods=['GET'])
@supabase_auth_required
def get_weekly_chart_data():
    """Get daily expenses or contributions data for a specific week"""
    try:
        user_id = request.current_user_id
        start_timestamp = request.args.get('start')  # Expected unix timestamp
        end_timestamp = request.args.get('end')  # Expected unix timestamp
        transaction_type = request.args.get('type', 'expense')  # 'expense' or 'contribution'
        
        print(f"Getting weekly chart data for user: {user_id}, type: {transaction_type}, start: {start_timestamp}, end: {end_timestamp}")
        
        from datetime import datetime, timedelta
        
        # Convert timestamps to datetime objects
        if start_timestamp and end_timestamp:
            start_date = datetime.fromtimestamp(int(start_timestamp) / 1000)
            end_date = datetime.fromtimestamp(int(end_timestamp) / 1000)
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
        else:
            return jsonify({'error': 'Start and end dates are required'}), 400
        
        # Initialize data structure with zeros for all days of the week (0-6 for Sunday-Saturday)
        data = [{'dayOfWeek': day, 'total': 0} for day in range(7)]
        
        # Process expenses or contributions depending on type
        if transaction_type.lower() == 'expense':
            # Get HSA documents (expenses) for this user within date range
            documents_endpoint = f"{supabase.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&expense_date=gte.{start_date_str}&expense_date=lte.{end_date_str}&select=*"
            documents_response = requests.get(documents_endpoint, headers=supabase.service_headers)
            
            if documents_response.status_code != 200:
                print(f"Error getting HSA documents: {documents_response.text}")
                return jsonify(data)  # Return initialized data with zeros
            
            documents = documents_response.json()
            print(f"Found {len(documents)} HSA documents for the week")
            
            # Process each document and update the corresponding day's total
            for doc in documents:
                if doc.get('expense_date') and doc.get('amount'):
                    try:
                        expense_date = datetime.strptime(doc['expense_date'], '%Y-%m-%d')
                        day_of_week = expense_date.weekday()  # 0-6 (Monday-Sunday)
                        # Convert to 0-6 (Sunday-Saturday) format to match frontend
                        day_of_week = (day_of_week + 1) % 7
                        
                        # Update the total for this day
                        data[day_of_week]['total'] += float(doc['amount'])
                    except (ValueError, TypeError) as e:
                        print(f"Error parsing expense date {doc.get('expense_date')}: {e}")
                        continue
        
        elif transaction_type.lower() == 'contribution':
            # Get HSA contributions for this user within date range
            contributions_endpoint = f"{supabase.url}/rest/v1/hsa_contributions?user_id=eq.{user_id}&contribution_date=gte.{start_date_str}&contribution_date=lte.{end_date_str}&select=*"
            contributions_response = requests.get(contributions_endpoint, headers=supabase.service_headers)
            
            if contributions_response.status_code != 200:
                print(f"Error getting HSA contributions: {contributions_response.text}")
                return jsonify(data)  # Return initialized data with zeros
            
            contributions = contributions_response.json()
            print(f"Found {len(contributions)} HSA contributions for the week")
            
            # Process each contribution and update the corresponding day's total
            for contrib in contributions:
                if contrib.get('contribution_date') and contrib.get('amount'):
                    try:
                        contrib_date = datetime.strptime(contrib['contribution_date'], '%Y-%m-%d')
                        day_of_week = contrib_date.weekday()  # 0-6 (Monday-Sunday)
                        # Convert to 0-6 (Sunday-Saturday) format to match frontend
                        day_of_week = (day_of_week + 1) % 7
                        
                        # Update the total for this day
                        data[day_of_week]['total'] += float(contrib['amount'])
                    except (ValueError, TypeError) as e:
                        print(f"Error parsing contribution date {contrib.get('contribution_date')}: {e}")
                        continue
        
        print(f"Generated weekly data: {data}")
        return jsonify(data)
        
    except Exception as e:
        print(f"Error in get_weekly_chart_data: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify([]), 500


@app.route('/api/hsa/yearly-chart-data', methods=['GET'])
@supabase_auth_required
def get_yearly_chart_data():
    """Get monthly expenses or contributions data for a specific year"""
    try:
        user_id = request.current_user_id

        # Safely parse the year parameter (default to current year if missing/invalid)
        from datetime import datetime, timedelta
        import calendar
        year_param = request.args.get('year')
        try:
            year = int(year_param) if year_param else datetime.now().year
        except ValueError:
            print(f"Invalid year parameter: {year_param}, defaulting to current year")
            year = datetime.now().year

        transaction_type = request.args.get('type', 'expense')  # 'expense' or 'contribution'
        
        print(f"Getting yearly chart data for user: {user_id}, type: {transaction_type}, year: {year}")
        
        # Initialize data structure with zeros for all months
        monthly_data = []
        for month in range(1, 13):  # 1-12 for all months
            monthly_data.append({
                'month': str(month),
                'month_name': calendar.month_abbr[month],
                'expenses': 0,
                'contributions': 0
            })
        
        # Start and end date for the year
        start_date_str = f"{year}-01-01"
        end_date_str = f"{year}-12-31"
        
        # Process expenses or contributions depending on type
        if transaction_type.lower() == 'expense' or transaction_type.lower() == 'both':
            # Get HSA documents (expenses) for this user within date range
            documents_endpoint = f"{supabase.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&expense_date=gte.{start_date_str}&expense_date=lte.{end_date_str}&select=*"
            documents_response = requests.get(documents_endpoint, headers=supabase.service_headers)
            
            if documents_response.status_code == 200:
                documents = documents_response.json()
                print(f"Found {len(documents)} HSA documents for the year {year}")
                
                # Group by month
                for doc in documents:
                    if doc.get('expense_date') and doc.get('amount'):
                        try:
                            expense_date = datetime.strptime(doc['expense_date'], '%Y-%m-%d')
                            month_index = expense_date.month - 1  # 0-11 index
                            monthly_data[month_index]['expenses'] += float(doc['amount'])
                        except (ValueError, TypeError) as e:
                            print(f"Error parsing expense date {doc.get('expense_date')}: {e}")
                            continue
            else:
                print(f"Error getting HSA documents: {documents_response.text}")
        
        if transaction_type.lower() == 'contribution' or transaction_type.lower() == 'both':
            # Get HSA contributions for this user within date range
            contributions_endpoint = f"{supabase.url}/rest/v1/hsa_contributions?user_id=eq.{user_id}&contribution_date=gte.{start_date_str}&contribution_date=lte.{end_date_str}&select=*"
            contributions_response = requests.get(contributions_endpoint, headers=supabase.service_headers)
            
            if contributions_response.status_code == 200:
                contributions = contributions_response.json()
                print(f"Found {len(contributions)} HSA contributions for the year {year}")
                
                # Group by month
                for contrib in contributions:
                    if contrib.get('contribution_date') and contrib.get('amount'):
                        try:
                            contrib_date = datetime.strptime(contrib['contribution_date'], '%Y-%m-%d')
                            month_index = contrib_date.month - 1  # 0-11 index
                            monthly_data[month_index]['contributions'] += float(contrib['amount'])
                        except (ValueError, TypeError) as e:
                            print(f"Error parsing contribution date {contrib.get('contribution_date')}: {e}")
                            continue
            else:
                print(f"Error getting HSA contributions: {contributions_response.text}")
        
        print(f"Generated yearly data for {year}: {monthly_data}")
        return jsonify(monthly_data)
        
    except Exception as e:
        print(f"Error in get_yearly_chart_data: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify([]), 500


@app.route('/api/hsa/ocr-process', methods=['POST'])
@supabase_auth_required
def process_ocr_only():
    """
    OCR-only endpoint that processes an image and returns extracted data
    without saving anything to the database
    """
    try:
        current_user_id = request.current_user_id
        print(f"\n===== OCR PROCESSING REQUEST =====\nUser ID: {current_user_id}")
        
        # Check if file is present
        if 'file' not in request.files:
            print("Error: No file provided")
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            print("Error: No file selected")
            return jsonify({'error': 'No file selected'}), 400
        
        print(f"Processing file: {file.filename}")
        print(f"File content type: {file.content_type}")
        
        # Read file data
        file_data = file.read()
        file_size = len(file_data)
        print(f"File size: {file_size} bytes")
        
        # Process image with OCR to extract document fields
        print("\n----- STARTING OCR PROCESSING -----")
        if gemini_processor:
            ocr_result = gemini_processor.process_receipt_image(file_data)
            if not ocr_result['success']:
                print("Gemini OCR failed, falling back to pytesseract...")
                ocr_result = ocr_processor.process_image(file_data)
        else:
            ocr_result = ocr_processor.process_image(file_data)
        print("----- OCR PROCESSING COMPLETE -----\n")
        
        print(f"OCR Success: {ocr_result['success']}")
        print(f"OCR Confidence: {ocr_result.get('confidence', 'unknown')}")
        
        return jsonify({
            'success': True,
            'ocr_result': ocr_result
        }), 200
        
    except Exception as e:
        print(f"OCR processing error: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'ocr_result': {
                'success': False,
                'error': str(e),
                'extracted_data': ocr_processor._get_default_fields()
            }
        }), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8088)
