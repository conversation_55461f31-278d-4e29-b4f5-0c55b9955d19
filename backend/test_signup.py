#!/usr/bin/env python3

import requests

def test_signup():
    """Test user signup"""
    
    signup_url = "http://localhost:8088/api/auth/signup"
    signup_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    
    print("Signing up...")
    signup_response = requests.post(signup_url, json=signup_data)
    print(f"Signup response: {signup_response.status_code}")
    print(f"Signup response text: {signup_response.text}")
    
    if signup_response.status_code == 201:
        print("✅ Signup successful!")
        return True
    else:
        print("❌ Signup failed!")
        return False

if __name__ == "__main__":
    test_signup()
