#!/usr/bin/env python3

import requests
import os
from io import BytesIO

def test_hsa_upload():
    """Test HSA document upload"""
    
    # First, let's get a valid JWT token by logging in
    login_url = "http://localhost:8088/api/auth/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"  # Adjust as needed
    }
    
    print("Logging in...")
    login_response = requests.post(login_url, json=login_data)
    print(f"Login response: {login_response.status_code}")
    print(f"Login response text: {login_response.text}")
    
    if login_response.status_code != 200:
        print("❌ Login failed!")
        return
    
    login_result = login_response.json()
    token = login_result.get('access_token')
    
    if not token:
        print("❌ No access token received!")
        return
    
    print(f"✅ Login successful, token: {token[:50]}...")
    
    # Create a test image file
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    # Prepare upload data
    upload_url = "http://localhost:8088/api/hsa/documents"
    
    files = {
        'file': ('test_receipt.png', BytesIO(test_image_data), 'image/png')
    }
    
    data = {
        'title': 'Test Medical Receipt',
        'description': 'Test upload for medical expense',
        'amount': '150.00',
        'category': 'Medical',
        'expense_date': '2025-06-08'
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    print("\nUploading HSA document...")
    upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
    print(f"Upload response: {upload_response.status_code}")
    print(f"Upload response text: {upload_response.text}")
    
    if upload_response.status_code == 201:
        print("✅ Upload successful!")
    else:
        print("❌ Upload failed!")

if __name__ == "__main__":
    test_hsa_upload()
