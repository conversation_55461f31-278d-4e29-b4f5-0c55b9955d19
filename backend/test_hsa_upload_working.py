#!/usr/bin/env python3

import requests
from io import BytesIO

def test_hsa_upload():
    """Test HSA document upload with valid token"""
    
    # Use the token from the successful login
    token = "eyJhbGciOiJIUzI1NiIsImtpZCI6IjVUa2Q3Q1YyNnh2S0lYMWMiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-TeFAi-u3-1g0rQLQE-fL9VrqaAMWDa5WzXPTsdCQio"
    
    # Create a test image file (small PNG)
    test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82'
    
    # Prepare upload data
    upload_url = "http://localhost:8088/api/hsa/documents"
    
    files = {
        'file': ('test_receipt.png', BytesIO(test_image_data), 'image/png')
    }
    
    data = {
        'title': 'Test Medical Receipt',
        'description': 'Test upload for medical expense',
        'amount': '150.00',
        'category': 'Medical',
        'expense_date': '2025-06-08'
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    print("Uploading HSA document...")
    print(f"Upload URL: {upload_url}")
    print(f"Data: {data}")
    print(f"Token: {token[:50]}...")
    
    upload_response = requests.post(upload_url, files=files, data=data, headers=headers)
    print(f"Upload response status: {upload_response.status_code}")
    print(f"Upload response text: {upload_response.text}")
    
    if upload_response.status_code == 201:
        print("✅ Upload successful!")
        
        # Now check if the document was saved to the database
        get_url = "http://localhost:8088/api/hsa/documents"
        get_response = requests.get(get_url, headers=headers)
        print(f"\nGet documents response: {get_response.status_code}")
        print(f"Documents: {get_response.text}")
        
    else:
        print("❌ Upload failed!")

if __name__ == "__main__":
    test_hsa_upload()
