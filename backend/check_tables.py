#!/usr/bin/env python3

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_supabase_tables():
    """Check what tables exist in Supabase"""
    url = os.getenv("SUPABASE_URL")
    service_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not service_key:
        print("❌ Missing Supabase configuration!")
        return
    
    # Check profiles table
    print("Checking profiles table...")
    endpoint = f"{url}/rest/v1/profiles"
    headers = {
        "apikey": service_key,
        "Authorization": f"Bearer {service_key}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(endpoint, headers=headers)
        print(f"Profiles table response: {response.status_code}")
        if response.status_code == 200:
            profiles = response.json()
            print(f"✅ Profiles table exists with {len(profiles)} records")
            if profiles:
                print(f"Sample profile: {profiles[0]}")
        else:
            print(f"❌ Profiles table error: {response.text}")
    except Exception as e:
        print(f"❌ Error checking profiles: {str(e)}")
    
    # Check hsa_documents table
    print("\nChecking hsa_documents table...")
    endpoint = f"{url}/rest/v1/hsa_documents"
    
    try:
        response = requests.get(endpoint, headers=headers)
        print(f"HSA documents table response: {response.status_code}")
        if response.status_code == 200:
            docs = response.json()
            print(f"✅ HSA documents table exists with {len(docs)} records")
            if docs:
                print(f"Sample document: {docs[0]}")
        else:
            print(f"❌ HSA documents table error: {response.text}")
    except Exception as e:
        print(f"❌ Error checking hsa_documents: {str(e)}")

if __name__ == "__main__":
    check_supabase_tables()
