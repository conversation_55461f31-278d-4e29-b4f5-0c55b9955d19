import os
import requests
import json
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()

class SupabaseClient:
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.service_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.anon_key = os.getenv("SUPABASE_KEY") or os.getenv("SUPABASE_ANON_KEY")
        
        if not self.service_key:
            raise ValueError("Supabase service key not found. Please set SUPABASE_SERVICE_KEY in your environment variables.")
        if not self.anon_key:
            raise ValueError("Supabase anon key not found. Please set SUPABASE_KEY or SUPABASE_ANON_KEY in your environment variables.")
            
        # Service role headers for admin operations
        self.service_headers = {
            "apikey": self.service_key,
            "Authorization": f"Bearer {self.service_key}",
            "Content-Type": "application/json"
        }
        
        # Anonymous headers for normal operations
        self.headers = {
            "apikey": self.anon_key,
            "Authorization": f"Bearer {self.anon_key}",
            "Content-Type": "application/json"
        }
    
    def sign_up(self, email, password):
        """Register a new user with email and password without verification"""
        # First create the user with the service role
        admin_endpoint = f"{self.url}/auth/v1/admin/users"
        admin_data = {
            "email": email,
            "password": password,
            "email_confirm": True,
            "user_metadata": {
                "email_verified": True
            },
            "app_metadata": {
                "provider": "email",
                "providers": ["email"]
            }
        }
        
        try:
            # Create user with service role
            admin_response = requests.post(admin_endpoint, headers=self.service_headers, json=admin_data)
            admin_response.raise_for_status()
            user_data = admin_response.json()
            
            # Immediately sign in the user
            auth_endpoint = f"{self.url}/auth/v1/token?grant_type=password"
            auth_data = {
                "email": email,
                "password": password
            }
            
            auth_response = requests.post(auth_endpoint, headers=self.headers, json=auth_data)
            auth_response.raise_for_status()
            
            # Combine the responses
            return {
                **user_data,
                **auth_response.json()
            }
            
        except Exception as e:
            print(f"Signup error: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                error_data = e.response.json()
                error_msg = error_data.get('msg') or error_data.get('error_description') or error_data.get('error')
                return {"error": error_msg}
            return {"error": str(e)}
    
    def sign_in(self, email, password):
        """Sign in a user with email and password"""
        # First try to sign in directly
        endpoint = f"{self.url}/auth/v1/token?grant_type=password"
        data = {
            "email": email,
            "password": password,
            "gotrue_meta_security": {
                "captcha_token": "bypass"
            }
        }
        try:
            print(f"Attempting Supabase login for {email}")
            print(f"Endpoint: {endpoint}")
            response = requests.post(endpoint, headers=self.headers, json=data)
            print(f"Sign-in response status: {response.status_code}")
            print(f"Sign-in response headers: {dict(response.headers)}")
            print(f"Sign-in response body: {response.text}")
            
            if response.status_code >= 400:
                error_data = response.json()
                error_msg = error_data.get('msg') or error_data.get('error_description') or error_data.get('error')
                print(f"Sign-in error: {error_msg}")
                return {"error": error_msg}
            
            return response.json()
        except Exception as e:
            print(f"Sign-in exception: {str(e)}")
            return {"error": str(e)}

    
    def create_profile(self, user_id, profile_data, auth_token=None):
        """Create a user profile in the profiles table"""
        endpoint = f"{self.url}/rest/v1/profiles"
        
        # Add authorization header if provided
        headers = self.headers.copy()
        if auth_token:
            headers["Authorization"] = f"Bearer {auth_token}"
        
        # Add user_id to profile data
        profile_data["id"] = user_id
        
        response = requests.post(endpoint, headers=headers, json=profile_data)
        return response.json() if response.text else {"status": "success"}
    
    def get_profile(self, user_id, auth_token=None):
        """Get a user profile from the profiles table"""
        endpoint = f"{self.url}/rest/v1/profiles?id=eq.{user_id}"
        
        # Always use service key for profile retrieval to bypass RLS
        headers = self.service_headers.copy()
        
        print(f"Getting profile for user: {user_id}")
        print(f"Endpoint: {endpoint}")
        
        response = requests.get(endpoint, headers=headers)
        print(f"Get profile response status: {response.status_code}")
        
        return response.json()
        
    def update_profile(self, user_id, profile_data, auth_token=None):
        """Update a user profile in the profiles table"""
        endpoint = f"{self.url}/rest/v1/profiles?id=eq.{user_id}"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        if auth_token:
            headers["Authorization"] = f"Bearer {auth_token}"
        
        # Remove id from profile_data if present (can't update primary key)
        if "id" in profile_data:
            del profile_data["id"]
        
        response = requests.patch(endpoint, headers=headers, json=profile_data)
        print(f"Update profile response status: {response.status_code}")
        print(f"Update profile response: {response.text}")
        
        return response.json() if response.text else {"status": "success"}
    
    def get_user(self, auth_token):
        """Get user information from Supabase Auth using JWT token"""
        # Try to validate the token by making a request to the profiles table
        # If the token is valid, this should work; if not, it will fail
        endpoint = f"{self.url}/rest/v1/profiles"
        
        headers = {
            "apikey": self.anon_key,
            "Authorization": f"Bearer {auth_token}",
            "Content-Type": "application/json"
        }
        
        print(f"Making request to: {endpoint}")
        print(f"Token being used: {auth_token[:50]}...")
        
        response = requests.get(endpoint, headers=headers)
        print(f"Response status: {response.status_code}")
        print(f"Response text: {response.text[:200]}...")
        
        if response.status_code == 200:
            # Token is valid, try to extract user info from the JWT
            try:
                import jwt
                import json
                # Decode JWT without verification to get user info
                decoded = jwt.decode(auth_token, options={"verify_signature": False})
                print(f"Decoded JWT: {decoded}")
                return {"user": {"id": decoded.get("sub"), "email": decoded.get("email")}}
            except Exception as e:
                print(f"JWT decode error: {e}")
                # Fallback - return a basic user object
                return {"user": {"id": "unknown"}}
        else:
            return {"error": f"Invalid token - Status: {response.status_code}, Response: {response.text}"}
    
    # HSA Document Management Methods
    def upload_hsa_document(self, user_id, document_data, auth_token=None):
        """Upload HSA document metadata to the hsa_documents table"""
        endpoint = f"{self.url}/rest/v1/hsa_documents"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        
        # Add user_id to document data
        document_data["user_id"] = user_id
        document_data["created_at"] = datetime.utcnow().isoformat()
        
        print(f"Uploading HSA document to: {endpoint}")
        print(f"Document data: {document_data}")
        print(f"Using service key for database operation")
        
        try:
            response = requests.post(endpoint, headers=headers, json=document_data)
            print(f"Upload response status: {response.status_code}")
            print(f"Upload response text: {response.text}")
            print(f"Upload response headers: {dict(response.headers)}")
            
            if response.status_code in [200, 201]:
                result = response.json() if response.text else {"status": "success"}
                print(f"Document uploaded successfully: {result}")
                return result
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('message') or error_data.get('error_description') or error_data.get('error') or response.text
                except:
                    error_msg = response.text or f"HTTP {response.status_code}"
                print(f"Upload error: {error_msg}")
                return {"error": error_msg}
            
        except Exception as e:
            print(f"Upload exception: {str(e)}")
            return {"error": str(e)}
    
    def get_hsa_documents(self, user_id, auth_token=None):
        """Get all HSA documents for a user"""
        endpoint = f"{self.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&order=created_at.desc"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        
        print(f"Getting HSA documents for user: {user_id}")
        print(f"Endpoint: {endpoint}")
        
        response = requests.get(endpoint, headers=headers)
        print(f"Get documents response status: {response.status_code}")
        print(f"Get documents response: {response.text}")
        
        return response.json()
    
    def get_monthly_hsa_documents(self, user_id, year, month, auth_token=None):
        """Get HSA documents for a specific month and year"""
        # Create date range for the specified month
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        endpoint = f"{self.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&expense_date=gte.{start_date}&expense_date=lt.{end_date}&order=expense_date.desc"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        
        print(f"Getting monthly HSA documents for user: {user_id}, {year}-{month:02d}")
        print(f"Endpoint: {endpoint}")
        
        response = requests.get(endpoint, headers=headers)
        print(f"Get monthly documents response status: {response.status_code}")
        print(f"Get monthly documents response: {response.text}")
        
        return response.json()
    
    def get_hsa_document(self, document_id, user_id, auth_token=None):
        """Get a specific HSA document by ID (with user verification)"""
        endpoint = f"{self.url}/rest/v1/hsa_documents?id=eq.{document_id}&user_id=eq.{user_id}"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        
        response = requests.get(endpoint, headers=headers)
        return response.json()
    
    def update_hsa_document(self, document_id, user_id, update_data, auth_token=None):
        """Update HSA document metadata"""
        endpoint = f"{self.url}/rest/v1/hsa_documents?id=eq.{document_id}&user_id=eq.{user_id}"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        
        update_data["updated_at"] = datetime.utcnow().isoformat()
        
        response = requests.patch(endpoint, headers=headers, json=update_data)
        return response.json() if response.text else {"status": "success"}
    
    def delete_hsa_document(self, document_id, user_id, auth_token=None):
        """Delete HSA document record"""
        endpoint = f"{self.url}/rest/v1/hsa_documents?id=eq.{document_id}&user_id=eq.{user_id}"
        
        # Use service key for database operations to bypass RLS
        headers = self.service_headers.copy()
        
        response = requests.delete(endpoint, headers=headers)
        return response.status_code == 204
    
    def upload_file_to_storage(self, file_data, file_path, auth_token=None):
        """Upload file to Supabase Storage"""
        endpoint = f"{self.url}/storage/v1/object/hsa-documents/{file_path}"
        
        # Determine content type based on file extension
        file_extension = file_path.split('.')[-1].lower()
        content_type_map = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'pdf': 'application/pdf',
            'webp': 'image/webp'
        }
        content_type = content_type_map.get(file_extension, 'application/octet-stream')
        
        headers = {
            "apikey": self.anon_key,
            "Authorization": f"Bearer {auth_token or self.anon_key}",
            "Content-Type": content_type,
            "x-upsert": "true"  # Allow overwriting existing files
        }
        
        print(f"Uploading file to: {endpoint}")
        print(f"File size: {len(file_data)} bytes")
        print(f"File path: {file_path}")
        print(f"Content-Type: {content_type}")
        
        try:
            # Use data parameter instead of files for raw file data
            response = requests.post(endpoint, headers=headers, data=file_data)
            
            print(f"Upload response status: {response.status_code}")
            print(f"Upload response text: {response.text}")
            
            if response.status_code in [200, 201]:
                return {"status": "success", "file_path": file_path}
            else:
                error_msg = response.text if response.text else f"HTTP {response.status_code}"
                print(f"Upload failed with error: {error_msg}")
                return {"error": error_msg}
        except Exception as e:
            print(f"Upload exception: {str(e)}")
            return {"error": str(e)}
    
    def get_file_url(self, file_path):
        """Get public URL for uploaded file"""
        return f"{self.url}/storage/v1/object/public/hsa-documents/{file_path}"
    
    def delete_file_from_storage(self, file_path, auth_token=None):
        """Delete file from Supabase Storage"""
        endpoint = f"{self.url}/storage/v1/object/hsa-documents/{file_path}"
        
        headers = {
            "apikey": self.anon_key,
            "Authorization": f"Bearer {auth_token or self.anon_key}",
        }
        
        response = requests.delete(endpoint, headers=headers)
        return response.status_code == 200
        
    def change_password(self, current_password, new_password, auth_token):
        """Change password for a user with their current password and a new password
        
        Args:
            current_password (str): Current user password for verification
            new_password (str): New password to set
            auth_token (str): JWT auth token for the current user
            
        Returns:
            dict: Response from Supabase Auth API
        """
        # First verify the current password by trying to re-authenticate
        # To do this, we need to get the user's email from the auth token
        try:
            # Get user info from token
            user_info = self.get_user(auth_token)
            if 'error' in user_info:
                return {"error": "Invalid or expired token"}
                
            user_email = user_info.get('user', {}).get('email')
            if not user_email:
                return {"error": "Could not retrieve user email from token"}
                
            # Try to sign in with current password to verify it
            sign_in_result = self.sign_in(user_email, current_password)
            if 'error' in sign_in_result:
                return {"error": "Current password is incorrect"}
                
            # If verification successful, update the password
            update_endpoint = f"{self.url}/auth/v1/user"
            update_headers = {
                "apikey": self.anon_key,
                "Authorization": f"Bearer {auth_token}",
                "Content-Type": "application/json"
            }
            
            update_data = {"password": new_password}
            
            print(f"Updating password for user")
            update_response = requests.put(update_endpoint, headers=update_headers, json=update_data)
            
            if update_response.status_code == 200:
                return {"success": True, "message": "Password updated successfully"}
            else:
                error_data = update_response.json()
                error_msg = error_data.get('msg') or error_data.get('error_description') or error_data.get('error') or "Failed to update password"
                return {"error": error_msg}
                
        except Exception as e:
            print(f"Password change error: {str(e)}")
            return {"error": str(e)}

    def execute_query(self, table, filters=None, method='select'):
        """Execute a query against the Supabase REST API
        
        Args:
            table (str): The table name to query
            filters (dict): Dictionary of filters to apply (e.g. {'column': 'eq.value'})
            method (str): The HTTP method to use (select, insert, update, delete)
            
        Returns:
            list: The query results
        """
        endpoint = f"{self.url}/rest/v1/{table}"
        
        # Build query parameters
        params = {}
        if filters:
            for key, value in filters.items():
                params[key] = value
                
        # Make the request
        try:
            if method == 'select':
                response = requests.get(endpoint, headers=self.headers, params=params)
            elif method == 'insert':
                response = requests.post(endpoint, headers=self.headers, json=filters)
            elif method == 'update':
                response = requests.patch(endpoint, headers=self.headers, json=filters)
            elif method == 'delete':
                response = requests.delete(endpoint, headers=self.headers, params=params)
            else:
                raise ValueError(f"Unsupported method: {method}")
                
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Query error: {str(e)}")
            return []
            if len(email_parts) > 1:
                email = email_parts[1].strip().strip("'").strip('"')
                endpoint += f"?email=eq.{email}"
        
        # Add authorization header if provided
        headers = self.headers.copy()
        if auth_token:
            headers["Authorization"] = f"Bearer {auth_token}"
            
        print(f"Executing query against endpoint: {endpoint}")
        response = requests.get(endpoint, headers=headers)
        
        try:
            return response.json()
        except:
            print(f"Failed to parse JSON response: {response.text}")
            return []
    
    def get_hsa_summary(self, user_id, year=None, auth_token=None):
        """
        Calculate HSA balance and yearly contributions for a user
        
        Args:
            user_id (str): The user ID to get the summary for
            year (int, optional): Year to calculate summary for (defaults to current year)
            auth_token (str, optional): Auth token for the request
            
        Returns:
            dict: HSA summary including balance, yearly contribution, and max contribution
        """
        from datetime import datetime
        
        try:
            # Determine target year (default to current year if not provided)
            current_year = year if year else datetime.now().year
            
            # Get all documents for this user
            endpoint = f"{self.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&select=*"
            
            # Use service key for database operations to bypass RLS
            headers = self.service_headers.copy()
            
            print(f"Getting HSA summary for user: {user_id}")
            print(f"Endpoint: {endpoint}")
            
            response = requests.get(endpoint, headers=headers)
            if response.status_code != 200:
                print(f"Error getting HSA documents: {response.text}")
                return {
                    "balance": 0,
                    "yearlyContribution": 0,
                    "maxContribution": 0
                }
            
            documents = response.json()
            print(f"Found {len(documents)} HSA documents for user {user_id}")
            
            # Debug: Print all documents to see their structure
            for i, doc in enumerate(documents[:5]):  # Print first 5 docs only to avoid log spam
                print(f"Document {i}: {doc}")
            
            # Filter out any documents with invalid amounts
            valid_documents = [doc for doc in documents if doc.get('amount') is not None]
            print(f"Found {len(valid_documents)} documents with valid amounts")
            
            # Calculate yearly expenses (sum of expenses for current year only)
            current_year_docs = [doc for doc in valid_documents 
                               if doc.get('expense_date') and 
                               datetime.strptime(doc['expense_date'], '%Y-%m-%d').year == current_year]
            
            print(f"Found {len(current_year_docs)} documents for current year {current_year}")
            
            # Calculate yearly expenses (only for the current year)
            yearly_expenses = sum(float(doc.get('amount', 0)) for doc in current_year_docs)
            print(f"Yearly expenses (current year only): ${yearly_expenses}")
            
            # Get real yearly contribution from contributions table (for current year only)
            yearly_contribution = self.get_yearly_contribution_total(user_id, current_year, auth_token)
            max_contribution = 0     # Individual HSA contribution limit
            
            # Default max contribution for the year
            max_contribution = 0
            
            return {
                "totalExpenses": yearly_expenses,  # Changed to use yearly expenses instead of all-time total
                "yearlyExpenses": yearly_expenses,
                "yearlyContribution": yearly_contribution,
                "maxContribution": max_contribution,

            }
            
        except Exception as e:
            print(f"Error calculating HSA summary: {str(e)}")
            return {
                "totalExpenses": 0,
                "yearlyExpenses": 0,
                "yearlyContribution": 0,
                "maxContribution": 0,

            }

    def add_hsa_contribution(self, user_id, amount, contribution_date=None, description=None, auth_token=None):
        """
        Add a new HSA contribution for a user
        
        Args:
            user_id (str): The user ID
            amount (float): The contribution amount
            contribution_date (str, optional): Date of contribution (YYYY-MM-DD format)
            description (str, optional): Description of the contribution
            auth_token (str, optional): Auth token for the request
            
        Returns:
            dict: The created contribution record or error
        """
        from datetime import datetime
        
        try:
            endpoint = f"{self.url}/rest/v1/hsa_contributions"
            
            # Use service key for database operations to bypass RLS
            headers = self.service_headers.copy()
            
            # Prepare contribution data
            contribution_data = {
                "user_id": user_id,
                "amount": float(amount),
                "description": description
            }
            
            # Set contribution date (default to today if not provided)
            if contribution_date:
                contribution_data["contribution_date"] = contribution_date
            else:
                contribution_data["contribution_date"] = datetime.now().strftime('%Y-%m-%d')
            
            print(f"Adding HSA contribution for user: {user_id}")
            print(f"Contribution data: {contribution_data}")
            
            response = requests.post(endpoint, headers=headers, json=contribution_data)
            print(f"Add contribution response status: {response.status_code}")
            print(f"Add contribution response: {response.text}")
            
            if response.status_code == 201:
                return response.json()[0] if response.json() else {"status": "success"}
            else:
                return {"error": f"Failed to add contribution: {response.text}"}
                
        except Exception as e:
            print(f"Error adding HSA contribution: {str(e)}")
            return {"error": str(e)}
    
    def get_hsa_contributions(self, user_id, year=None, auth_token=None):
        """
        Get HSA contributions for a user
        
        Args:
            user_id (str): The user ID
            year (int, optional): Filter by specific year
            auth_token (str, optional): Auth token for the request
            
        Returns:
            list: List of contribution records
        """
        try:
            endpoint = f"{self.url}/rest/v1/hsa_contributions?user_id=eq.{user_id}&order=contribution_date.desc"
            
            # Add year filter if specified
            if year:
                start_date = f"{year}-01-01"
                end_date = f"{year + 1}-01-01"
                endpoint += f"&contribution_date=gte.{start_date}&contribution_date=lt.{end_date}"
            
            # Use service key for database operations to bypass RLS
            headers = self.service_headers.copy()
            
            print(f"Getting HSA contributions for user: {user_id}")
            print(f"Endpoint: {endpoint}")
            
            response = requests.get(endpoint, headers=headers)
            print(f"Get contributions response status: {response.status_code}")
            print(f"Get contributions response: {response.text}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error getting contributions: {response.text}")
                return []
                
        except Exception as e:
            print(f"Error getting HSA contributions: {str(e)}")
            return []
    
    def get_yearly_contribution_total(self, user_id, year=None, auth_token=None):
        """
        Get total HSA contributions for a user for a specific year
        
        Args:
            user_id (str): The user ID
            year (int, optional): Year to calculate total for (defaults to current year)
            auth_token (str, optional): Auth token for the request
            
        Returns:
            float: Total contribution amount for the year
        """
        from datetime import datetime
        
        if not year:
            year = datetime.now().year
            
        contributions = self.get_hsa_contributions(user_id, year, auth_token)
        
        total = sum(float(contrib.get('amount', 0)) for contrib in contributions)
        print(f"Total contributions for {year}: ${total}")
        
        return total
    
    def delete_hsa_contribution(self, contribution_id, user_id, auth_token=None):
        """
        Delete an HSA contribution record
        
        Args:
            contribution_id (str): The contribution ID to delete
            user_id (str): The user ID (for verification)
            auth_token (str, optional): Auth token for the request
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            endpoint = f"{self.url}/rest/v1/hsa_contributions?id=eq.{contribution_id}&user_id=eq.{user_id}"
            
            # Use service key for database operations to bypass RLS
            headers = self.service_headers.copy()
            
            print(f"Deleting HSA contribution: {contribution_id} for user: {user_id}")
            
            response = requests.delete(endpoint, headers=headers)
            print(f"Delete contribution response status: {response.status_code}")
            
            return response.status_code == 204
            
        except Exception as e:
            print(f"Error deleting HSA contribution: {str(e)}")
            return False
            
    def get_hsa_contribution_by_id(self, contribution_id, user_id, auth_token=None):
        """
        Get a specific HSA contribution by ID
        
        Args:
            contribution_id (str): The contribution ID to retrieve
            user_id (str): The user ID (for verification)
            auth_token (str, optional): Auth token for the request
            
        Returns:
            dict: The contribution record or None if not found
        """
        try:
            endpoint = f"{self.url}/rest/v1/hsa_contributions?id=eq.{contribution_id}&user_id=eq.{user_id}"
            
            # Use service key for database operations to bypass RLS
            headers = self.service_headers.copy()
            
            print(f"Getting HSA contribution: {contribution_id} for user: {user_id}")
            
            response = requests.get(endpoint, headers=headers)
            print(f"Get contribution response status: {response.status_code}")
            
            if response.status_code == 200 and response.json():
                return response.json()[0]
            else:
                return None
                
        except Exception as e:
            print(f"Error getting HSA contribution: {str(e)}")
            return None

    def get_recent_transactions(self, user_id):
        try:
            # Get current month's start date
            current_month_start = datetime.now().replace(day=1).strftime('%Y-%m-%d')
            
            # Get this month's transactions
            endpoint_monthly = f"{self.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&created_at=gte.{current_month_start}&order=created_at.desc"
            
            # Use service key for database operations to bypass RLS
            headers = self.service_headers.copy()
            
            monthly_response = requests.get(endpoint_monthly, headers=headers)
            if monthly_response.status_code != 200:
                print(f"Failed to fetch monthly transactions: {monthly_response.status_code}")
                return {'error': f'Failed to fetch monthly transactions: {monthly_response.status_code}'}
                
            monthly_transactions = monthly_response.json()
            
            # Get last 3 recent transactions
            endpoint_recent = f"{self.url}/rest/v1/hsa_documents?user_id=eq.{user_id}&order=created_at.desc&limit=3"
            recent_response = requests.get(endpoint_recent, headers=headers)
            
            if recent_response.status_code != 200:
                print(f"Failed to fetch recent transactions: {recent_response.status_code}")
                return {'error': f'Failed to fetch recent transactions: {recent_response.status_code}'}
                
            recent_items = recent_response.json()
            
            # Format transactions for frontend using the same format as transactionService.formatTransaction
            formatted_monthly = []
            for tx in monthly_transactions:
                formatted_monthly.append({
                    'id': tx.get('id'),
                    'title': tx.get('title') or tx.get('file_name') or '',
                    'vendor': tx.get('vendor') or tx.get('title') or tx.get('file_name') or 'Unknown Vendor',
                    'amount': float(tx.get('amount') or 0),
                    'category': tx.get('category') or 'Medical',
                    'date': tx.get('expense_date') or tx.get('created_at'),
                    'status': tx.get('status') or 'pending',
                    'description': tx.get('description')
                })
                
            formatted_recent = []
            for tx in recent_items:
                formatted_recent.append({
                    'id': tx.get('id'),
                    'title': tx.get('title') or tx.get('file_name') or '',
                    'vendor': tx.get('vendor') or tx.get('title') or tx.get('file_name') or 'Unknown Vendor',
                    'amount': float(tx.get('amount') or 0),
                    'category': tx.get('category') or 'Medical',
                    'date': tx.get('expense_date') or tx.get('created_at'),
                    'status': tx.get('status') or 'pending',
                    'description': tx.get('description')
                })
            
            return {
                'monthly_transactions': formatted_monthly,
                'recent_items': formatted_recent
            }
        except Exception as e:
            print(f"Error getting recent transactions: {str(e)}")
            # Return empty arrays with the expected structure
            return {
                'monthly_transactions': [],
                'recent_items': []
            }

    def get_current_year_contributions(self, user_id):
        """Get HSA contributions for the current year for a specific user"""
        current_year = datetime.now().year
        endpoint = f"{self.url}/rest/v1/hsa_contributions"
        
        params = {
            'select': '*',
            'user_id': f'eq.{user_id}',
            'contribution_date': f'gte.{current_year}-01-01',
            'order': 'contribution_date.desc'
        }
        
        try:
            response = requests.get(
                endpoint,
                headers=self.service_headers,
                params=params
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting contributions: {str(e)}")
            return []

    def refresh_token(self, refresh_token):
        """Refresh an access token using a refresh token"""
        endpoint = f"{self.url}/auth/v1/token?grant_type=refresh_token"
        data = {
            "refresh_token": refresh_token
        }
        
        # Log the refresh attempt (with partial token for security)
        token_preview = f"{refresh_token[:5]}...{refresh_token[-5:]}" if len(refresh_token) > 10 else "[invalid token]"
        print(f"🔄 Supabase: Refreshing token {token_preview} at endpoint {endpoint}")
        
        try:
            # Make the request to Supabase
            print(f"🔄 Supabase: Sending refresh request with headers: {self.headers}")
            response = requests.post(endpoint, headers=self.headers, json=data)
            
            # Log response status
            print(f"🔄 Supabase: Refresh response status: {response.status_code}")
            
            # Handle error responses
            if response.status_code >= 400:
                try:
                    error_data = response.json()
                    print(f"🔄 Supabase: Error response body: {error_data}")
                    error_msg = error_data.get('msg') or error_data.get('error_description') or error_data.get('error') or 'Unknown error'
                    return {"error": error_msg}
                except Exception as json_error:
                    print(f"🔄 Supabase: Failed to parse error response: {str(json_error)}")
                    return {"error": f"Invalid response from server (status {response.status_code})"}
            
            # Parse successful response
            try:
                result = response.json()
                print(f"🔄 Supabase: Successful refresh. New access token: {result.get('access_token', '')[:5]}...")
                return result
            except Exception as json_error:
                print(f"🔄 Supabase: Failed to parse success response: {str(json_error)}")
                return {"error": "Failed to parse authentication response"}
                
        except Exception as e:
            print(f"🔄 Supabase: Token refresh exception: {str(e)}")
            return {"error": f"Connection error: {str(e)}"}

# Create client instance
supabase_client = SupabaseClient()

def get_client():
    """Return the Supabase client instance."""
    return supabase_client
