import React, { useState, useEffect, useCallback, useRef } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, TextInput, View } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { SupabaseAuthProvider } from './src/context/SupabaseAuthContext';
import * as Font from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);
  const navigationRef = useRef(null);
  
  // Make navigation reference globally available for use in AuthContext
  global.navigationRef = navigationRef;

  useEffect(() => {
    async function prepare() {
      try {
        // Load fonts
        await Font.loadAsync({
          'Geist-Regular': require('./assets/fonts/Geist-Regular.otf'),
          'Geist-Medium': require('./assets/fonts/Geist-Medium.otf'),
          'Geist-Bold': require('./assets/fonts/Geist-Bold.otf'),
        });

        // Override default Text and TextInput components with Geist font
        const TextRender = Text.render;
        const TextInputRender = TextInput.render;
        
        Text.render = function render(props) {
          return TextRender.call(this, {
            ...props,
            style: [{ fontFamily: 'Geist-Regular' }, props.style],
          });
        };
        
        TextInput.render = function render(props) {
          return TextInputRender.call(this, {
            ...props,
            style: [{ fontFamily: 'Geist-Regular' }, props.style],
          });
        };
      } catch (e) {
        console.warn(e);
      } finally {
        // Tell the application to render
        setAppIsReady(true);
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = useCallback(async () => {
    if (appIsReady) {
      // This tells the splash screen to hide immediately
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SupabaseAuthProvider>
        <View style={styles.container} onLayout={onLayoutRootView}>
          <AppNavigator ref={navigationRef} />
          <StatusBar style="auto" />
        </View>
      </SupabaseAuthProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
});
