import { useState, useEffect } from 'react';

export const usePriceHistory = (cryptocurrency, years = 5) => {
  const [priceHistory, setPriceHistory] = useState([]);

  useEffect(() => {
    const generateMockData = () => {
      const data = [];
      const now = new Date();
      const totalDays = years * 365;

      // Ethereum key price points and events
      const ethHistoricalEvents = {
        2015: { startPrice: 1, trend: 'stable' },
        2016: { startPrice: 10, trend: 'growth' },
        2017: { startPrice: 10, peakPrice: 1400, trend: 'bull' },
        2018: { startPrice: 1000, lowPrice: 85, trend: 'bear' },
        2019: { startPrice: 100, peakPrice: 350, trend: 'recovery' },
        2020: { startPrice: 130, peakPrice: 750, trend: 'bull' },
        2021: { startPrice: 730, peakPrice: 4800, trend: 'bull' },
        2022: { startPrice: 3700, lowPrice: 880, trend: 'bear' },
        2023: { startPrice: 1200, peakPrice: 2100, trend: 'recovery' },
        2024: { startPrice: 2000, trend: 'volatile' },
        2025: { startPrice: 2200, trend: 'volatile' }
      };

      const generatePrice = (basePrice, dayIndex, year) => {
        const event = ethHistoricalEvents[year];
        let volatility = 0.02; // Base daily volatility
        let trendMultiplier = 1;

        switch(event.trend) {
          case 'bull':
            volatility = 0.035;
            trendMultiplier = 1.003;
            break;
          case 'bear':
            volatility = 0.04;
            trendMultiplier = 0.997;
            break;
          case 'recovery':
            volatility = 0.025;
            trendMultiplier = 1.001;
            break;
          case 'growth':
            volatility = 0.02;
            trendMultiplier = 1.002;
            break;
          case 'stable':
            volatility = 0.015;
            trendMultiplier = 1;
            break;
          case 'volatile':
            volatility = 0.045;
            trendMultiplier = Math.random() > 0.5 ? 1.002 : 0.998;
            break;
        }

        const dailyChange = (Math.random() - 0.5) * 2 * volatility * basePrice;
        return Math.max((basePrice + dailyChange) * trendMultiplier, 0.01);
      };

      let currentPrice = ethHistoricalEvents[now.getFullYear()].startPrice;

      for (let i = totalDays; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const year = date.getFullYear();

        if (cryptocurrency?.toLowerCase() === 'eth') {
          currentPrice = generatePrice(currentPrice, i, year);
        } else {
          // Fallback for other cryptocurrencies with simplified mock data
          const volatility = 0.02;
          const dailyChange = (Math.random() - 0.5) * 2 * volatility * currentPrice;
          currentPrice = Math.max(currentPrice + dailyChange, 0.01);
        }

        // Store one data point per week to reduce data size
        if (i % 7 === 0) {
          data.push({
            x: date.toISOString(),
            y: parseFloat(currentPrice.toFixed(2))
          });
        }
      }
      
      return data;
    };

    setPriceHistory(generateMockData());
  }, [cryptocurrency, years]);

  return priceHistory;
};