import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from './apiClient';
import config from '../config/config';
import * as SecureStore from 'expo-secure-store';

const API_BASE_URL = config.API_BASE_URL;
const TOKEN_KEY = 'auth_token'; // Same key used in apiClient.js

class HSADocumentService {
  // No longer need these methods as apiClient handles tokens automatically

  async uploadDocument(file, documentData) {
    try {
      console.log('HSA Service - Starting document upload...');
      
      const formData = new FormData();

      // Add file
      formData.append('file', {
        uri: file.uri,
        type: file.type || 'image/jpeg',
        name: file.name || 'document.jpg',
      });

      // Add metadata - only include non-empty values to allow OCR to fill defaults
      if (documentData.title && documentData.title.trim()) {
        formData.append('title', documentData.title);
      }
      if (documentData.description && documentData.description.trim()) {
        formData.append('description', documentData.description);
      }
      if (documentData.amount && documentData.amount > 0) {
        formData.append('amount', documentData.amount.toString());
      }
      if (documentData.category && documentData.category.trim()) {
        formData.append('category', documentData.category);
      }
      if (documentData.expense_date && documentData.expense_date.trim()) {
        formData.append('expense_date', documentData.expense_date);
      }
      
      const response = await apiClient.post(`${API_BASE_URL}/hsa/documents`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.status === 200 || response.status === 201) {
        return response.data;
      } else {
        throw new Error(`Failed to upload document: ${response.status}`);
      }

    } catch (error) {
      console.error('Upload document error:', error);
      return { error: error.message || 'Failed to upload document', status: 500 };
    }
  }

  async processImageOCR(imageFile) {
    try {
      // Retrieve a valid access token (SecureStore ➜ AsyncStorage session ➜ legacy auth_token)
      let accessToken = null;

      try {
        const sessionData = await SecureStore.getItemAsync('supabase_session');
        if (sessionData) {
          const sessionObj = JSON.parse(sessionData);
          accessToken = sessionObj?.access_token;
        }
      } catch (e) {
        console.log('SecureStore not available, falling back to AsyncStorage for session');
      }

      if (!accessToken) {
        try {
          const sessionData = await AsyncStorage.getItem('supabase_session');
          if (sessionData) {
            const sessionObj = JSON.parse(sessionData);
            accessToken = sessionObj?.access_token;
          }
        } catch (e) {
          console.log('AsyncStorage session retrieval failed', e);
        }
      }

      // Legacy fallback – get token from TOKEN_KEY if still used
      if (!accessToken) {
        accessToken = await AsyncStorage.getItem(TOKEN_KEY);
      }

      if (!accessToken) {
        throw new Error('Authentication token not found – please log in again.');
      }

      const formData = new FormData();
      formData.append('file', imageFile);

      const response = await fetch(`${API_BASE_URL}/hsa/ocr-process`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('OCR processing error:', error);
      throw error;
    }
  }

  async getDocuments() {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/documents`);
      
      if (response.status === 200) {
        return response.data.documents || [];
      } else {
        console.log('Failed to fetch documents:', response.status);
        return [];
      }
    } catch (error) {
      console.error('Get documents error:', error);
      // Return empty array instead of throwing error
      return [];
    }
  }

  async getDocument(documentId) {
    try {
      const response = await apiClient.get(`${API_BASE_URL}/hsa/documents/${documentId}`);

      if (response.status === 200) {
        return { success: true, data: response.data.document };
      } else {
        throw new Error('Failed to fetch document');
      }
    } catch (error) {
      console.error('Get document error:', error);
      return { success: false, error: error.message };
    }
  }

  async updateDocument(documentId, updateData) {
    try {
      const response = await apiClient.put(`${API_BASE_URL}/hsa/documents/${documentId}`, updateData);

      if (response.status === 200) {
        return { success: true, data: response.data };
      } else {
        throw new Error('Failed to update document');
      }
    } catch (error) {
      console.error('Update document error:', error);
      return { success: false, error: error.message };
    }
  }

  async deleteDocument(documentId) {
    try {
      const response = await apiClient.delete(`${API_BASE_URL}/hsa/documents/${documentId}`);
      
      if (response.status === 200 || response.status === 204) {
        return { success: true };
      } else {
        throw new Error(`Failed to delete document: ${response.status}`);
      }
    } catch (error) {
      console.error('Delete document error:', error);
      return { success: false, error: error.message || 'Failed to delete document' };
    }
  }
}

export default new HSADocumentService();
