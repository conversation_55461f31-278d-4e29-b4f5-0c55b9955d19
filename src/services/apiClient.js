import axios from 'axios';
import config from '../config/config';
import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API configuration
const API_BASE_URL = config.API_BASE_URL;

// Storage keys
const SESSION_KEY = 'supabase_session';

// Create axios instance with basic configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Secure storage helpers
const getSecureData = async (key) => {
  try {
    const value = await SecureStore.getItemAsync(key);
    if (value) {
      return JSON.parse(value);
    }
  } catch (error) {
    console.error(`❌ Error retrieving ${key} from secure storage:`, error);
  }
  
  // Fallback to AsyncStorage
  try {
    const value = await AsyncStorage.getItem(key);
    if (value) {
      return JSON.parse(value);
    }
  } catch (error) {
    console.error(`❌ Error retrieving ${key} from AsyncStorage fallback:`, error);
  }
  
  return null;
};

// Request interceptor - add auth token
apiClient.interceptors.request.use(
  async (config) => {
    console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    
    // Skip auth for refresh endpoint
    if (config.url?.includes('/auth/refresh')) {
      return config;
    }

    // Get current session
    const session = await getSecureData(SESSION_KEY);
    if (session?.access_token) {
      config.headers.Authorization = `Bearer ${session.access_token}`;
      console.log('🔐 Added auth token to request');
    } else {
      console.log('⚠️ No auth token available for request');
    }
    
    return config;
  },
  (error) => {
    console.error('🌐 API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor - handle responses and token expiration
apiClient.interceptors.response.use(
  (response) => {
    console.log(`🌐 API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  async (error) => {
    console.error('🌐 API Response Error:', error.response?.status, error.config?.url);
    
    const originalRequest = error.config;

    // If we get a 401 and haven't already tried to refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      console.log('🔄 Received 401, attempting to refresh session...');
      
      try {
        const session = await getSecureData(SESSION_KEY);
        if (session?.refresh_token) {
          // Attempt to refresh the token
          const refreshResponse = await fetch(`${API_BASE_URL.replace('/api', '')}/api/auth/refresh`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              refresh_token: session.refresh_token
            })
          });

          if (refreshResponse.ok) {
            const data = await refreshResponse.json();
            
            if (!data.error) {
              // Update session with new tokens
              const newSession = {
                access_token: data.access_token,
                refresh_token: data.refresh_token,
                expires_at: data.expires_at,
                expires_in: data.expires_in,
                token_type: data.token_type || 'bearer',
                user: data.user
              };

              // Store the new session
              await SecureStore.setItemAsync(SESSION_KEY, JSON.stringify(newSession));
              console.log('✅ Session refreshed successfully');

              // Retry the original request with new token
              originalRequest.headers.Authorization = `Bearer ${newSession.access_token}`;
              return apiClient(originalRequest);
            }
          }
        }
        
        console.log('❌ Session refresh failed - token may be invalid');
      } catch (refreshError) {
        console.error('❌ Error during token refresh:', refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
