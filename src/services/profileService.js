import apiClient from './apiClient';

const profileService = {
  /**
   * Get the current user's profile
   * @returns {Promise} Promise object with profile data
   */
  getProfile: async () => {
    try {
      const response = await apiClient.get('/profile');
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Re-throw so callers can handle (e.g. navigate to AuthResetScreen on 401)
      throw error;
    }
  },

  /**
   * Update the current user's profile
   * @param {Object} profileData - The profile data to update
   * @returns {Promise} Promise object with updated profile data
   */
  updateProfile: async (profileData) => {
    try {
      const response = await apiClient.put('/profile', profileData);
      return response.data;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  },

  /**
   * Upload a profile picture
   * @param {Object} imageData - The image data to upload
   * @returns {Promise} Promise object with image URL
   */
  uploadProfilePicture: async (imageData) => {
    try {
      // This is a placeholder for image upload functionality
      // In a real implementation, you would upload the image to a storage service
      // and return the URL
      return { imageUrl: 'https://example.com/profile-picture.jpg' };
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  },
  
  /**
   * Delete the current user's account
   * @returns {Promise} Promise resolving to success status
   */
  deleteAccount: async () => {
    try {
      const response = await apiClient.delete('/profile/account');
      return response.data;
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  }
};

export default profileService;
