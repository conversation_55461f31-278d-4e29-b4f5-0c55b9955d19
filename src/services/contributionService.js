import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from './apiClient';
import config from '../config/config';

const API_BASE_URL = config.API_BASE_URL;

class ContributionService {

  async addContribution(amount, contributionDate = null, description = null) {
    try {
      const contributionData = {
        amount: parseFloat(amount),
        description: description
      };

      if (contributionDate) {
        contributionData.contribution_date = contributionDate;
      }

      const response = await apiClient.post(`${API_BASE_URL}/hsa/contributions`, contributionData);
      
      if (response.status === 200 || response.status === 201) {
        console.log('Contribution Service - Contribution added successfully');
        return response.data;
      } else {
        throw new Error(`Failed to add contribution: ${response.status}`);
      }

    } catch (error) {
      console.error('Error in addContribution:', error);
      return { error: error.message || 'Failed to add contribution', status: 500 };
    }
  }

  async getContributions(year = null) {
    try {
      let params = {};
      if (year) {
        params.year = year;
      }

      console.log('🔄 Fetching contributions from API...');

      const response = await apiClient.get(`${API_BASE_URL}/hsa/contributions`, { params });
      
      if (response.status === 200) {
        const data = response.data;
        
        if (data && data.contributions && Array.isArray(data.contributions)) {
          console.log(`✅ Received ${data.contributions.length} contributions from API`);
          return data.contributions;
        } else if (data && Array.isArray(data)) {
          console.log(`✅ Received ${data.length} contributions from API (direct array)`);
          return data;
        } else {
          console.log('⚠️ API returned data but no contributions array found:', data);
          return [];
        }
      } else {
        console.log('⚠️ Failed to fetch contributions:', response.status);
        return [];
      }
    } catch (error) {
      console.error('❌ Error in getContributions:', error.message || error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
      // Return empty array instead of throwing error
      return [];
    }
  }

  async deleteContribution(contributionId) {
    try {
      const response = await apiClient.delete(`${API_BASE_URL}/hsa/contributions/${contributionId}`);

      if (response.status === 200 || response.status === 204) {
        return { success: true };
      } else {
        throw new Error(`Failed to delete contribution: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting contribution:', error);
      return { error: error.message || 'Failed to delete contribution', status: 500 };
    }
  }

  async getContributionById(contributionId) {
    try {
      console.log(`Fetching contribution with ID: ${contributionId}`);
      
      // Try to get the contribution directly from the API
      const response = await apiClient.get(`${API_BASE_URL}/hsa/contributions/${contributionId}`);
      
      if (response.status === 200) {
        console.log('Successfully retrieved contribution');
        return response.data;
      } else {
        console.log('API call failed, falling back to filtering all contributions');
        
        // Fallback: Get all contributions and filter
        const allContributions = await this.getContributions();
        
        if (Array.isArray(allContributions)) {
          const contribution = allContributions.find(c => c.id === contributionId);
          
          if (contribution) {
            console.log('Successfully found contribution through fallback');
            return contribution;
          } else {
            throw new Error(`Contribution with ID ${contributionId} not found`);
          }
        } else {
          throw new Error('Failed to fetch contributions');
        }
      }
    } catch (error) {
      console.error('Error in getContributionById:', error);
      throw error;
    }
  }

  async getYearlyTotal(year = null) {
    try {
      let params = {};
      if (year) {
        params.year = year;
      }

      const response = await apiClient.get(`${API_BASE_URL}/hsa/contributions/total`, { params });

      if (response.status === 200) {
        return response.data;
      } else {
        throw new Error('Failed to fetch yearly total');
      }
    } catch (error) {
      console.error('Error fetching yearly total:', error);
      throw error;
    }
  }

  formatContribution(contribution) {
    return {
      id: contribution.id,
      amount: parseFloat(contribution.amount || 0),
      date: contribution.contribution_date,
      description: contribution.description || 'HSA Contribution',
      formattedAmount: `$${parseFloat(contribution.amount || 0).toFixed(2)}`,
      formattedDate: new Date(contribution.contribution_date).toLocaleDateString(),
    };
  }
}

export default new ContributionService();
