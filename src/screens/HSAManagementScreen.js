import React, { useState, useEffect, useCallback, useRef } from 'react';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity, 
  ScrollView, 
  FlatList,
  TextInput,
  Alert,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  Modal
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import { StatusBar } from 'expo-status-bar';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import hsaDocumentService from '../services/hsaDocumentService';
import transactionService from '../services/transactionService';
import contributionService from '../services/contributionService';
import ContributionModal from '../components/ContributionModal';
import ContributionsList from '../components/ContributionsList';
import HSA<PERSON><PERSON> from '../components/HSAChart';

const HSAManagementScreen = ({ navigation, route }) => {
  const { logout, refreshSession } = useSupabaseAuth();
  const hsaChartRef = useRef(null);
  const [hsaData, setHsaData] = useState({
    totalExpenses: 0,
    yearlyExpenses: 0,
    yearlyContribution: 0,
    maxContribution: 0,
    currentYear: new Date().getFullYear()
  });
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [yearPickerVisible, setYearPickerVisible] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [monthlyTransactions, setMonthlyTransactions] = useState([]);
  const [contributionModalVisible, setContributionModalVisible] = useState(false);
  const [contributionsRefreshTrigger, setContributionsRefreshTrigger] = useState(0);
  const [contributions, setContributions] = useState([]);
  const [monthlyChartData, setMonthlyChartData] = useState([]);
  const [chartLoading, setChartLoading] = useState(false);
  const [currentWeekDate, setCurrentWeekDate] = useState(new Date());

  // Generate years from 1950 to current year
  const generateAllYears = () => {
    const years = [];
    const currentYear = new Date().getFullYear();
    for (let year = 1950; year <= currentYear; year++) {
      years.push(year);
    }
    return years;
  };

  const allYears = generateAllYears();

  const loadHSASummary = async () => {
    try {
      console.log(`📊 Loading HSA summary for year ${selectedYear}...`);
      const summaryData = await transactionService.getHSASummary(selectedYear);

      if (summaryData) {
        const newHsaData = {
          totalExpenses: parseFloat(summaryData.totalExpenses || summaryData.total_expenses || 0),
          yearlyExpenses: parseFloat(summaryData.yearlyExpenses || summaryData.yearly_expenses || 0),
          yearlyContribution: parseFloat(summaryData.yearlyContribution || summaryData.yearly_contribution || 0),
          maxContribution: parseFloat(summaryData.maxContribution || summaryData.max_contribution || 0),
          currentYear: selectedYear
        };

        setHsaData(newHsaData);
        console.log(`✅ HSA summary updated for ${selectedYear}:`, newHsaData);
      }
    } catch (error) {
      console.error('❌ Error loading HSA summary:', error);
    }
  };

  const loadDocuments = async () => {
    try {
      const response = await hsaDocumentService.getDocuments();
      
      if (response && Array.isArray(response.documents)) {
        // Filter documents for the selected year
        const yearDocuments = response.documents.filter(doc => {
          const expenseDate = new Date(doc.expense_date);
          return expenseDate.getFullYear() === selectedYear;
        });
        setDocuments(yearDocuments);
      }
    } catch (error) {
      console.error('Error loading documents:', error);
    }
  };

  const loadContributions = async () => {
    try {
      console.log('Loading HSA contributions...');
      // Get contributions for the selected year from API
      const yearContributions = await contributionService.getContributions(selectedYear);
      
      if (yearContributions && Array.isArray(yearContributions)) {
        const formattedContributions = yearContributions.map(contribution => ({
          id: contribution.id,
          amount: parseFloat(contribution.amount || 0),
          date: contribution.contribution_date,
          formattedDate: new Date(contribution.contribution_date).toLocaleDateString(),
          formattedAmount: `$${parseFloat(contribution.amount || 0).toFixed(2)}`,
          description: contribution.description || 'HSA Contribution'
        }));
        setContributions(formattedContributions);
        console.log(`✅ Loaded ${formattedContributions.length} contributions for ${selectedYear}`);
      } else {
        console.log('⚠️ No contributions data returned or invalid format');
        setContributions([]);
      }
    } catch (error) {
      console.error('❌ Error loading contributions:', error);
      setContributions([]);
    }
  };

  const loadMonthlyTransactions = async () => {
    try {
      const response = await transactionService.getThisMonthsTransactions();
      const transactions = response && response.transactions ? response.transactions : [];
      
      // Filter transactions for the selected year
      const yearTransactions = transactions.filter(transaction => {
        // Handle case where expense_date or date might be null or invalid
        if (!transaction.expense_date && !transaction.date) return false;
        
        const transactionDate = new Date(transaction.expense_date || transaction.date);
        return !isNaN(transactionDate) && transactionDate.getFullYear() === selectedYear;
      });
      
      const formattedTransactions = yearTransactions.map(transaction => {
        // Ensure all required fields have valid values
        return {
          id: transaction.id || `temp-${Math.random().toString(36).substring(7)}`,
          date: transaction.expense_date || transaction.date,
          amount: parseFloat(transaction.amount || 0),
          vendor: transaction.vendor || transaction.title || 'Unknown',
          category: transaction.category || 'Medical',
          status: transaction.status || 'pending',
          document_id: transaction.document_id || transaction.id
        };
      });
        
      setMonthlyTransactions(formattedTransactions);
      console.log('Monthly transactions loaded:', formattedTransactions.length);
    } catch (error) {
      console.error('Load monthly transactions error:', error);
      setMonthlyTransactions([]); // Set empty array on error
    }
  };

  const loadAllData = async () => {
    console.log(`🔄 Loading HSA data for year ${selectedYear}...`);
    setLoading(true);
    setChartLoading(true);
    
    try {
      // Execute these sequentially to avoid race conditions
      await loadHSASummary();
      await loadDocuments();
      await loadContributions();
      await loadMonthlyTransactions();
      
      // Load chart data for the selected year
      try {
        const yearlyData = await transactionService.getYearlyChartData(selectedYear, 'Expense');
        if (yearlyData && Array.isArray(yearlyData)) {
          setMonthlyChartData(yearlyData);
          console.log(`✅ Chart data loaded for year ${selectedYear}`);
        }
      } catch (chartError) {
        console.error('Error loading chart data:', chartError);
        setMonthlyChartData([]);
      }
      
      console.log('✅ HSA data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading HSA data:', error);
    } finally {
      setLoading(false);
      setChartLoading(false);
      
      // Small delay to ensure chart is rendered before setting current week
      setTimeout(() => {
        if (hsaChartRef.current) {
          if (hsaChartRef.current.setToSpecificWeek) {
            // Use the stored week date if available, otherwise default to current week
            hsaChartRef.current.setToSpecificWeek(currentWeekDate);
          } else {
            // Fallback to the original method
            hsaChartRef.current.setToCurrentWeek();
          }
        }
      }, 500);
    }
  };

  // Handle year selection
  const handleYearChange = (year) => {
    console.log(`🔄 Year changed to ${year}, reloading all data...`);

    // Prevent infinite loops during year change
    setIsChangingYear(true);

    setSelectedYear(year);
    setYearPickerVisible(false);

    // Update the current week date to reflect the new year
    // Keep the same week structure but change the year
    const newWeekDate = new Date(currentWeekDate);
    newWeekDate.setFullYear(year);

    // If the new date would be in the future (for current year), use today's date
    const today = new Date();
    const finalWeekDate = (year === today.getFullYear() && newWeekDate > today) ? today : newWeekDate;

    console.log(`📅 Updating currentWeekDate from ${currentWeekDate.toISOString()} to ${finalWeekDate.toISOString()}`);
    setCurrentWeekDate(finalWeekDate);

    // Force immediate reload of all data for the new year
    loadAllData();

    // Clear the year changing flag after a longer delay to ensure all updates complete
    setTimeout(() => {
      setIsChangingYear(false);
      console.log(`✅ HSAManagementScreen: Year change completed, isChangingYear set to false`);
    }, 1000);
  };

  useEffect(() => {
    loadAllData();
    // Small delay to ensure chart ref is available
    const timer = setTimeout(() => {
      ensureCurrentWeek();
    }, 300);
    return () => clearTimeout(timer);
  }, [ensureCurrentWeek, selectedYear]);

  // Track if we're in the middle of a year change to prevent loops
  const [isChangingYear, setIsChangingYear] = useState(false);

  useEffect(() => {
    if (route.params?.refresh || route.params?.newExpense) {
      console.log('🔄 Route params changed, refreshing...');
      loadAllData();
      navigation.setParams({ refresh: false, newExpense: false });
    }
  }, [route.params?.refresh, route.params?.newExpense]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshSession();
      await loadAllData();
    } catch (error) {
      console.error('Error during refresh:', error);
      Alert.alert('Refresh Failed', 'Could not update data. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [refreshSession]);

  // Year picker modal
  const renderYearPickerModal = () => (
    <Modal
      visible={yearPickerVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setYearPickerVisible(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.yearPickerContainer}>
          <View style={styles.yearPickerHeader}>
            <Text style={styles.yearPickerTitle}>Select Year</Text>
            <TouchableOpacity 
              style={styles.closeButtonContainer}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.yearPickerContent}>
            {/* Decades quick selector */}
            <View style={styles.decadesSelector}>
              {[1950, 1970, 1990, 2010, 2030].map(decade => (
                <TouchableOpacity
                  key={decade}
                  style={[
                    styles.decadeButton,
                    selectedYear >= decade && selectedYear < decade + 20 && styles.decadeButtonActive
                  ]}
                  onPress={() => setSelectedYear(decade)}
                >
                  <Text style={styles.decadeButtonText}>{decade}s</Text>
                </TouchableOpacity>
              ))}
            </View>
            
            {/* Year grid */}
            <View style={styles.yearGrid}>
              {allYears
                .filter(year => year >= Math.floor(selectedYear / 10) * 10 && year < Math.floor(selectedYear / 10) * 10 + 10)
                .map(year => (
                  <TouchableOpacity
                    key={year}
                    style={[
                      styles.yearGridItem,
                      selectedYear === year && styles.yearGridItemSelected
                    ]}
                    onPress={() => handleYearChange(year)}
                  >
                    <Text
                      style={[
                        styles.yearGridItemText,
                        selectedYear === year && styles.yearGridItemTextSelected,
                        year === new Date().getFullYear() && styles.currentYearText
                      ]}
                    >
                      {year}
                    </Text>
                  </TouchableOpacity>
                ))
              }
            </View>
          </View>
          
          <View style={styles.yearPickerActions}>
            <TouchableOpacity
              style={styles.yearPickerButton}
              onPress={() => handleYearChange(new Date().getFullYear())}
            >
              <Text style={styles.yearPickerButtonText}>Current Year</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.yearPickerButton, styles.yearPickerPrimaryButton]}
              onPress={() => setYearPickerVisible(false)}
            >
              <Text style={[styles.yearPickerButtonText, styles.yearPickerPrimaryButtonText]}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  const handleContributionAdded = () => {
    // Refresh HSA summary to show updated contribution data
    loadHSASummary();
    // Trigger contributions list refresh
    setContributionsRefreshTrigger(prev => prev + 1);
    // Also reload contributions to update the list
    loadContributions();
  };

  const handleContributionError = (errorMessage) => {
    Alert.alert('Error', errorMessage);
  };

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatAmount = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  const formatCurrency = (amount) => {
    return parseFloat(amount || 0).toFixed(2);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return '#28a745';
      case 'rejected':
        return '#dc3545';
      default:
        return '#007bff';
    }
  };

  const renderDocumentItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.documentItem}
      onPress={() => navigation.navigate('HSADocumentDetail', { documentId: item.id })}
    >
      <View style={styles.documentInfo}>
        <Text style={styles.documentName}>{item.title}</Text>
        <Text style={styles.documentDetails}>
          {formatAmount(item.amount)} • {item.category} • {formatDate(item.expense_date)}
        </Text>
        <Text style={styles.documentDate}>Uploaded: {formatDate(item.created_at)}</Text>
      </View>
      <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
        <Text style={styles.statusText}>{item.status?.toUpperCase() || 'PENDING'}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderMonthlyTransactionItem = ({ item }) => {
    // Safely format the date
    const formattedDate = item.date ? formatDate(item.date) : 'Unknown date';
    
    return (
      <TouchableOpacity 
        style={styles.transactionItem}
        onPress={() => navigation.navigate('HSADocumentDetail', { documentId: item.document_id || item.id })}
      >
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionDate}>{formattedDate}</Text>
          <Text style={styles.transactionDesc}>{item.vendor}</Text>
          <Text style={styles.transactionCategory}>{item.category}</Text>
        </View>
        <View style={styles.transactionAmount}>
          <Text style={[styles.transactionValue, styles.negativeAmount]}>
            -${(item.amount || 0).toFixed(2)}
          </Text>
          <Text style={styles.transactionStatus}>{item.status}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Add a function to ensure the chart shows the current week
  const ensureCurrentWeek = useCallback(() => {
    // Don't call chart methods during year changes to avoid conflicts
    if (hsaChartRef.current && !isChangingYear) {
      // Only call setToSpecificWeek if the current week date year matches the selected year
      if (hsaChartRef.current.setToSpecificWeek && currentWeekDate.getFullYear() === selectedYear) {
        console.log(`📅 ensureCurrentWeek: calling setToSpecificWeek with ${currentWeekDate.toISOString()} for year ${selectedYear}`);
        hsaChartRef.current.setToSpecificWeek(currentWeekDate);
      } else if (hsaChartRef.current.setToCurrentWeek) {
        console.log(`📅 ensureCurrentWeek: calling setToCurrentWeek`);
        hsaChartRef.current.setToCurrentWeek();
      } else {
        console.log(`⏸️ ensureCurrentWeek: skipped - year mismatch (currentWeekDate year: ${currentWeekDate.getFullYear()}, selectedYear: ${selectedYear})`);
      }
    } else {
      console.log(`⏸️ ensureCurrentWeek: skipped - isChangingYear: ${isChangingYear}, hasRef: ${!!hsaChartRef.current}`);
    }
  }, [currentWeekDate, isChangingYear, selectedYear]);

  // Call this function when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Ensure we're showing the current week when the screen is focused
      ensureCurrentWeek();
      return () => {};
    }, [ensureCurrentWeek])
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView} 
        refreshControl={
        <RefreshControl 
          refreshing={refreshing} 
          onRefresh={onRefresh} 
        />
      }
    >
      {loading ? (
        <ActivityIndicator 
          style={styles.loader} 
          size="large" 
          color="#4a6fa5" 
        />
      ) : (
        <View>
          <View style={styles.balanceCard}>
            <View style={styles.balanceHeader}>
              <View style={styles.cardTitleContainer}>
                <Text style={styles.balanceTitle}>HSA Summary</Text>
                <TouchableOpacity 
                  style={styles.yearSelectorButton}
                  onPress={() => setYearPickerVisible(true)}
                >
                  <Text style={styles.yearSelectorText}>{selectedYear}</Text>
                  <Text style={styles.yearSelectorIcon}>▼</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <Text style={styles.balanceAmount}>${hsaData.totalExpenses.toFixed(2)}</Text>
            <Text style={styles.balanceSubtitle}>Total Expenses</Text>
            
            <View style={styles.contributionInfo}>
              <View style={styles.contributionHeaderRow}>
                <Text style={styles.contributionText}>
                  Year-to-date expenses:
                </Text>
                <Text style={styles.contributionAmount}>
                  ${formatCurrency(hsaData.yearlyExpenses)}
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressBarFill, 
                    { width: `${Math.min((hsaData.yearlyExpenses / Math.max(hsaData.yearlyExpenses, 1)) * 100, 100)}%` }
                  ]} 
                />
              </View>
            </View>
            
            <View style={[styles.contributionInfo, {marginTop: 16}]}>
              <View style={styles.contributionHeaderRow}>
                <Text style={styles.contributionText}>
                  Year-to-date contributions:
                </Text>
                <Text style={styles.contributionAmount}>
                  ${formatCurrency(hsaData.yearlyContribution)}
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressBarFill, 
                    { 
                      width: `${Math.min((hsaData.yearlyContribution / (hsaData.maxContribution || 1)) * 100, 100)}%`, 
                      backgroundColor: '#4CAF50' 
                    }
                  ]} 
                />
              </View>
              <Text style={styles.contributionLimit}>
                Annual limit: ${formatCurrency(hsaData.maxContribution)}
              </Text>
            </View>
          </View>
                  
          <View style={styles.chartContainer}>
            <HSAChart 
              ref={hsaChartRef}
              data={monthlyChartData} 
              loading={chartLoading}
              defaultPeriod="week"
              initialWeek={currentWeekDate}
              selectedYear={selectedYear}
              onYearChange={(year) => {
                console.log(`HSAChart year changed to: ${year}`);
                // Don't process year changes if we're already changing years to avoid loops
                if (!isChangingYear) {
                  handleYearChange(year);
                }
              }}
              onWeekChange={(date) => {
                // Store the current week date for persistence between year changes
                console.log(`Week changed to: ${date.toISOString()}`);

                // Don't process week changes during year changes to avoid loops
                if (isChangingYear) {
                  console.log(`⏸️ Ignoring week change during year change`);
                  return;
                }

                // Only update if the date is actually different to avoid loops
                if (date.getTime() !== currentWeekDate.getTime()) {
                  setCurrentWeekDate(date);

                  // If the week change crosses year boundaries, update the selected year
                  const weekYear = date.getFullYear();
                  if (weekYear !== selectedYear) {
                    console.log(`Week navigation crossed year boundary: ${selectedYear} -> ${weekYear}`);
                    setSelectedYear(weekYear);
                    // Reload data for the new year
                    loadAllData();
                  }
                }
              }}
            />
          </View>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsRow}>
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => navigation.navigate('UploadReceipt')}
              >
                <Text style={styles.actionButtonIcon}>📷</Text>
                <Text style={styles.actionButtonText}>Upload Receipt</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => setContributionModalVisible(true)}
              >
                <Text style={styles.actionButtonIcon}>💰</Text>
                <Text style={styles.actionButtonText}>Add Contribution</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => navigation.navigate('Spending')}
              >
                <Text style={styles.actionButtonIcon}>📊</Text>
                <Text style={styles.actionButtonText}>View Spending</Text>
              </TouchableOpacity>
              

            </View>
          </View>
          <View style={styles.section}>
            <TouchableOpacity 
              style={styles.sectionHeader}
              onPress={() => navigation.navigate('HSAAllTransactions')}
            >
              <View style={styles.sectionTitleContainer}>
                <Text style={styles.sectionTitle}>This Month's Transactions</Text>
                <Text style={styles.sectionSubtitle}>View your spending history</Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color="#4a6fa5" />
            </TouchableOpacity>
            
            {monthlyTransactions.length === 0 ? (
              <TouchableOpacity 
                style={styles.emptyState}
                onPress={() => navigation.navigate('HSAAllTransactions')}
              >
                <Ionicons name="document-text-outline" size={40} color="#aaa" style={styles.emptyStateIcon} />
                <Text style={styles.emptyStateText}>No transactions this month</Text>
                <Text style={styles.emptyStateSubtext}>Tap to view all transactions</Text>
              </TouchableOpacity>
            ) : (
              <View>
                <FlatList
                  data={monthlyTransactions.slice(0, 3)}
                  renderItem={renderMonthlyTransactionItem}
                  keyExtractor={(item, index) => item.id ? item.id.toString() : index.toString()}
                  scrollEnabled={false}
                />
                {monthlyTransactions.length > 0 && (
                  <TouchableOpacity 
                    style={styles.viewMoreIndicator}
                    onPress={() => navigation.navigate('HSAAllTransactions')}
                  >
                    <Text style={styles.viewMoreText}>Tap to view all transactions</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>

          <View style={styles.section}>
            <TouchableOpacity 
              style={styles.sectionHeader}
              onPress={() => navigation.navigate('HSAContributionHistory')}
            >
              <View style={styles.sectionTitleContainer}>
                <Text style={styles.sectionTitle}>Recent Contributions</Text>
                <Text style={styles.sectionSubtitle}>View your contribution history</Text>
              </View>
              <Ionicons name="chevron-forward" size={24} color="#4a6fa5" />
            </TouchableOpacity>
            
            <ContributionsList
              contributions={contributions}
              refreshTrigger={contributionsRefreshTrigger}
              maxItems={3}
            />
            {contributions.length > 3 && (
              <TouchableOpacity 
                style={styles.viewMoreIndicator}
                onPress={() => navigation.navigate('HSAContributionHistory')}
              >
                <Text style={styles.viewMoreText}>View all contributions</Text>
              </TouchableOpacity>
            )}
          </View>
          
          <StatusBar style="auto" />
        </View>
      )}
      
      <ContributionModal
        visible={contributionModalVisible}
        onClose={() => setContributionModalVisible(false)}
        onContributionAdded={handleContributionAdded}
        onError={handleContributionError}
      />

      {/* Year Picker Modal */}
      {renderYearPickerModal()}

    </ScrollView>
  </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  chartContainer: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    minHeight: 200,
  },
  chartPlaceholder: {
  },
  scrollView: {
    flex: 1,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  balanceCard: {
    backgroundColor: '#4a6fa5',
    padding: 16,
    margin: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  balanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10
  },
  cardTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%'
  },
  balanceTitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontWeight: '600'
  },
  balanceSubtitle: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    marginTop: -5,
    marginBottom: 16
  },
  editButton: {
    padding: 5
  },
  balanceAmount: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  contributionInfo: {
    marginTop: 10,
  },
  contributionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6
  },
  contributionText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
  },
  contributionAmount: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600'
  },
  contributionLimit: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'right'
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitleContainer: {
    flex: 1,
  },
  sectionSubtitle: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  viewMoreIndicator: {
    backgroundColor: '#f8f9fa',
    padding: 8,
    borderRadius: 8,
    marginTop: 12,
    alignItems: 'center',
  },
  viewMoreText: {
    color: '#4a6fa5',
    fontSize: 13,
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  uploadButton: {
    backgroundColor: '#4a6fa5',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  uploadButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  documentDetails: {
    fontSize: 14,
    color: '#757575',
  },
  documentDate: {
    fontSize: 12,
    color: '#9e9e9e',
  },
  statusBadge: {
    width: 80,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#757575',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9e9e9e',
    textAlign: 'center',
  },
  emptyStateIcon: {
    marginBottom: 8,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDate: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  transactionDesc: {
    fontSize: 16,
    color: '#333',
  },
  transactionCategory: {
    fontSize: 14,
    color: '#333',
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  transactionValue: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  negativeAmount: {
    color: '#e53935',
  },
  positiveAmount: {
    color: '#43a047',
  },
  receiptLink: {
    fontSize: 14,
    color: '#4a6fa5',
  },
  transactionStatus: {
    fontSize: 14,
    color: '#333',
  },
  viewAllButton: {
    paddingVertical: 12,
    marginTop: 8,
    alignItems: 'center',
  },
  viewAllButtonText: {
    color: '#4a6fa5',
    fontSize: 16,
    fontWeight: '500',
  },
  actionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    gap: 8,
  },
  actionButton: {
    alignItems: 'center',
    padding: 12,
    flex: 1,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  actionButtonIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
  },
  yearSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 8,
  },
  yearSelectorText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  yearSelectorIcon: {
    fontSize: 10,
    color: '#fff',
    marginLeft: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearPickerContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    width: '80%',
    maxWidth: 400,
  },
  yearPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  yearPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButtonContainer: {
    padding: 5,
  },
  closeButton: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4a6fa5',
  },
  yearPickerContent: {
    marginBottom: 16,
  },
  decadesSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  decadeButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  decadeButtonActive: {
    backgroundColor: '#4a6fa5',
  },
  decadeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  yearGridItem: {
    width: '25%',
    padding: 8,
  },
  yearGridItemSelected: {
    backgroundColor: '#4a6fa5',
  },
  yearGridItemText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  yearGridItemTextSelected: {
    color: '#fff',
  },
  currentYearText: {
    color: '#4a6fa5',
  },
  yearPickerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  yearPickerButton: {
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  yearPickerButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  yearPickerPrimaryButton: {
    backgroundColor: '#4a6fa5',
  },
  yearPickerPrimaryButtonText: {
    color: '#fff',
  },
});

export default HSAManagementScreen;
