import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Dimensions,
  StatusBar,
  Platform,
  Share
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import contributionService from '../services/contributionService';

const { width, height } = Dimensions.get('window');

// Responsive sizing
const scale = size => (width / 375) * size; // Base width of 375 (iPhone 6/7/8)
const verticalScale = size => (height / 812) * size; // Base height of 812 (iPhone X/XS/11 Pro)
const moderateScale = (size, factor = 0.5) => size + (scale(size) - size) * factor;

// Modern color palette
const COLORS = {
  primary: '#4a6fa5', // Main blue
  primaryDark: '#3a5985', // Darker blue
  secondary: '#22C55E', // Green
  secondaryDark: '#16A34A', // Darker green
  danger: '#EF4444', // Red
  dangerDark: '#DC2626', // Darker red
  background: '#F0F9FF', // Very light blue
  surface: '#FFFFFF',
  surfaceAlt: '#F8FAFC', // Light gray-blue
  text: '#0F172A', // Very dark blue-gray
  textSecondary: '#64748B', // Blue-gray
  border: '#E2E8F0', // Light gray-blue
};

const ContributionDetailScreen = ({ navigation, route }) => {
  const { logout } = useSupabaseAuth();
  const { contributionId } = route.params;
  const [contribution, setContribution] = useState(null);
  const [loading, setLoading] = useState(true);
  const insets = useSafeAreaInsets();
  
  // Create styles with insets
  const styles = StyleSheet.create({
    ...createStyles(insets),
    header: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: 90 + (Platform.OS === 'ios' ? insets.top : StatusBar.currentHeight || 0),
      zIndex: 10,
      paddingTop: Platform.OS === 'ios' ? insets.top : StatusBar.currentHeight || 0,
      overflow: 'visible',
    },
    backButtonSmall: {
      position: 'absolute',
      top: Platform.OS === 'ios' ? insets.top + 12 : 24,
      left: 16,
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: 'rgba(255,255,255,0.2)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 20,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
  });

  useEffect(() => {
    loadContribution();
  }, [contributionId]);

  const loadContribution = async () => {
    try {
      setLoading(true);
      // Get the specific contribution by ID
      const result = await contributionService.getContributionById(contributionId);
      
      if (result && result.id) {
        setContribution(result);
      } else {
        console.error('Failed to load contribution details');
        Alert.alert('Error', 'Failed to load contribution details');
      }
    } catch (error) {
      console.error('Error loading contribution:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatAmount = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  const handleDelete = async () => {
    Alert.alert(
      'Delete Contribution',
      'Are you sure you want to delete this contribution? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await contributionService.deleteContribution(contributionId);
              if (result.success) {
                Alert.alert('Success', 'Contribution deleted successfully', [
                  { text: 'OK', onPress: () => navigation.goBack() }
                ]);
              } else {
                Alert.alert('Error', result.error || 'Failed to delete contribution');
              }
            } catch (error) {
              console.error('Error deleting contribution:', error);
              Alert.alert('Error', 'An unexpected error occurred');
            }
          }
        }
      ]
    );
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `HSA Contribution: ${formatAmount(contribution.amount)} on ${formatDate(contribution.contribution_date)}`,
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to share contribution details');
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <StatusBar barStyle="light-content" backgroundColor={COLORS.primary} />
        <LinearGradient
          colors={[COLORS.primary, COLORS.primaryDark]}
          style={styles.loadingContainer}
        >
          <View style={styles.loadingContent}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>Loading contribution details...</Text>
          </View>
        </LinearGradient>
      </View>
    );
  }

  if (!contribution) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <StatusBar barStyle="light-content" backgroundColor={COLORS.primary} />
        <View style={styles.errorContainer}>
          <View style={styles.errorIconContainer}>
            <Ionicons name="cash-outline" size={80} color={COLORS.primary} />
          </View>
          <Text style={styles.errorTitle}>Contribution Not Found</Text>
          <Text style={styles.errorSubtitle}>The contribution you're looking for doesn't exist or has been removed.</Text>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <LinearGradient
              colors={[COLORS.primary, COLORS.primaryDark]}
              style={styles.backButtonGradient}
            >
              <Text style={styles.backButtonText}>Go Back</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.primary} />
      
      {/* Header with Gradient Background */}
      <View style={styles.header}>
        <LinearGradient
          colors={[COLORS.primary, COLORS.primaryDark]}
          style={[StyleSheet.absoluteFill, { zIndex: 1 }]}
        />
        <TouchableOpacity
          style={styles.backButtonSmall}
          onPress={() => navigation.goBack()}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          <Ionicons name="chevron-back" size={28} color="#fff" />
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.scrollViewContent}>
          {/* Amount Card with Title */}
          <View style={styles.amountCard}>
            <Text style={styles.documentTitle}>
              HSA Contribution
            </Text>
            <View style={styles.amountRow}>
              <Text style={styles.amountLabel}>Contribution Amount</Text>
              <Text style={styles.amountValue}>{formatAmount(contribution.amount)}</Text>
            </View>
          </View>

          {/* Details Card */}
          <View style={styles.detailsCard}>
            <View style={styles.cardHeader}>
              <Ionicons name="cash-outline" size={22} color={COLORS.primary} style={styles.cardHeaderIcon} />
              <Text style={styles.cardHeaderTitle}>Contribution Details</Text>
            </View>
            
            <View style={styles.detailsGrid}>
              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="calendar-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Contribution Date</Text>
                  <Text style={styles.detailValue}>{formatDate(contribution.contribution_date)}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="create-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Description</Text>
                  <Text style={styles.detailValue}>{contribution.description || 'HSA Contribution'}</Text>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailIconContainer}>
                  <Ionicons name="time-outline" size={20} color={COLORS.primary} />
                </View>
                <View style={styles.detailContent}>
                  <Text style={styles.detailLabel}>Created At</Text>
                  <Text style={styles.detailValue}>{formatDate(contribution.created_at)}</Text>
                </View>
              </View>

              {contribution.tax_year && (
                <View style={styles.detailRow}>
                  <View style={styles.detailIconContainer}>
                    <Ionicons name="document-text-outline" size={20} color={COLORS.primary} />
                  </View>
                  <View style={styles.detailContent}>
                    <Text style={styles.detailLabel}>Tax Year</Text>
                    <Text style={styles.detailValue}>{contribution.tax_year}</Text>
                  </View>
                </View>
              )}
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionContainer}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => navigation.navigate('HSAContributionHistory', { editContribution: contribution })}
              activeOpacity={0.8}
            >
              <View style={[styles.actionButtonInner, styles.editButton]}>
                <Ionicons name="pencil-outline" size={22} color={COLORS.primary} />
                <Text style={[styles.buttonText, styles.editButtonText]}>Edit</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleShare}
              activeOpacity={0.8}
            >
              <View style={[styles.actionButtonInner, styles.shareButton]}>
                <Ionicons name="share-social-outline" size={22} color={COLORS.secondary} />
                <Text style={[styles.buttonText, styles.shareButtonText]}>Share</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleDelete}
              activeOpacity={0.8}
            >
              <View style={[styles.actionButtonInner, styles.deleteButton]}>
                <Ionicons name="trash-outline" size={22} color={COLORS.danger} />
                <Text style={[styles.buttonText, styles.deleteButtonText]}>Delete</Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={styles.bottomPadding} />
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (insets) => ({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingTop: 90, // Match header height
    paddingBottom: 20, // Add bottom padding for better scrolling
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 90,
    zIndex: 10,
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backButtonSmall: {
    // This will be overridden in the component styles
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.15)',
    padding: 25,
    borderRadius: 16,
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 16,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: COLORS.background,
  },
  errorIconContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: `${COLORS.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  errorTitle: {
    fontSize: 26,
    color: COLORS.text,
    fontWeight: '700',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  errorSubtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginBottom: 36,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: '85%',
  },
  backButton: {
    borderRadius: 30,
    overflow: 'hidden',
    shadowColor: COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  backButtonGradient: {
    paddingHorizontal: 36,
    paddingVertical: 15,
    borderRadius: 30,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  amountCard: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 24,
    marginTop: 20,
    marginBottom: 16,
    borderRadius: 14,
    padding: 20,
    shadowColor: 'rgba(0, 0, 0, 0.10)',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  amountRow: {
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 15,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  amountValue: {
    fontSize: 28,
    fontWeight: '700',
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: 4,
  },
  documentTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 26,
  },
  detailsCard: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 14,
    padding: 18,
    shadowColor: 'rgba(0, 0, 0, 0.06)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.10,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  cardHeader: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    paddingBottom: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardHeaderIcon: {
    marginRight: 10,
  },
  cardHeaderTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text,
  },
  detailsGrid: {
    marginBottom: 10,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  detailIconContainer: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    backgroundColor: `${COLORS.primary}10`,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 24,
    marginVertical: 10,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 6,
  },
  actionButtonInner: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
  },
  editButton: {
    backgroundColor: `${COLORS.primary}10`,
    borderColor: COLORS.primary,
  },
  shareButton: {
    backgroundColor: `${COLORS.secondary}10`,
    borderColor: COLORS.secondary,
  },
  deleteButton: {
    backgroundColor: `${COLORS.danger}10`,
    borderColor: COLORS.danger,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  editButtonText: {
    color: COLORS.primary,
  },
  shareButtonText: {
    color: COLORS.secondary,
  },
  deleteButtonText: {
    color: COLORS.danger,
  },
  bottomPadding: {
    height: 40,
  },
});

export default ContributionDetailScreen; 