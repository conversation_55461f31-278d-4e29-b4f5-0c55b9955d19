import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { useNavigation } from '@react-navigation/native';

// Token storage keys
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'user_data';
const SESSION_KEY = 'supabase_session';
const TOKEN_EXPIRY_KEY = 'token_expiry';

const AuthResetScreen = () => {
  const navigation = useNavigation();

  // Helper function to remove secure data
  const removeSecureData = async (key) => {
    try {
      await SecureStore.deleteItemAsync(key);
      console.log(`✅ Removed ${key} from secure storage`);
    } catch (error) {
      console.error(`❌ Error removing ${key} from secure storage:`, error);
    }
    
    // Also remove from AsyncStorage fallback
    try {
      await AsyncStorage.removeItem(key);
      console.log(`✅ Removed ${key} from AsyncStorage fallback`);
    } catch (fallbackError) {
      console.error(`❌ Error removing ${key} from AsyncStorage fallback:`, fallbackError);
    }
  };

  // Clear all auth tokens
  const clearAllTokens = async () => {
    console.log('🧹 Clearing all authentication tokens...');
    await removeSecureData(TOKEN_KEY);
    await removeSecureData(REFRESH_TOKEN_KEY);
    await removeSecureData(TOKEN_EXPIRY_KEY);
    await AsyncStorage.removeItem(USER_KEY);
    await AsyncStorage.removeItem(SESSION_KEY);
    console.log('🧹 All tokens cleared successfully');
  };

  // Handle reset and navigation
  const handleReset = async () => {
    try {
      await clearAllTokens();
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error during auth reset:', error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Authentication Reset</Text>
      <Text style={styles.description}>
        Your authentication session has expired or become invalid. This screen will help you reset your authentication state.
      </Text>

      <View style={styles.infoBox}>
        <Text style={styles.infoTitle}>What happened?</Text>
        <Text style={styles.infoText}>
          The app detected an issue with your authentication tokens. This can happen if:
        </Text>
        <Text style={styles.bulletPoint}>• Your session has expired</Text>
        <Text style={styles.bulletPoint}>• The authentication server rejected your tokens</Text>
        <Text style={styles.bulletPoint}>• There was a problem with token refresh</Text>
      </View>

      <TouchableOpacity style={styles.button} onPress={handleReset}>
        <Text style={styles.buttonText}>Reset Authentication & Go to Login</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
    color: '#555',
  },
  infoBox: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginBottom: 30,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 10,
    color: '#555',
  },
  bulletPoint: {
    fontSize: 14,
    marginLeft: 10,
    marginBottom: 5,
    color: '#555',
  },
  button: {
    backgroundColor: '#4a90e2',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default AuthResetScreen;
