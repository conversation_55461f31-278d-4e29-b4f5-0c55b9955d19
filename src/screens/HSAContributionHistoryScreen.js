import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  FlatList, 
  Text, 
  ActivityIndicator, 
  SafeAreaView,
  RefreshControl
} from 'react-native';
import transactionService from '../services/transactionService';

const HSAContributionHistoryScreen = () => {
  const [contributions, setContributions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadContributions = async () => {
    try {
      setLoading(true);
      const contributionsData = await transactionService.getCurrentYearContributions();
      
      if (contributionsData && Array.isArray(contributionsData)) {
        // Format contributions for display
        const formattedContributions = contributionsData.map(contribution => ({
          id: contribution.id,
          amount: parseFloat(contribution.amount || 0),
          date: contribution.contribution_date,
          formattedDate: new Date(contribution.contribution_date).toLocaleDateString(),
          formattedAmount: `$${parseFloat(contribution.amount || 0).toFixed(2)}`,
          description: contribution.description || 'HSA Contribution'
        }));
        
        setContributions(formattedContributions);
      } else {
        setContributions([]);
      }
    } catch (error) {
      console.error('Error loading contributions:', error);
      setContributions([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadContributions();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadContributions();
  };

  const renderContributionItem = ({ item }) => (
    <View style={styles.contributionItem}>
      <View style={styles.contributionDetails}>
        <Text style={styles.contributionDate}>{item.formattedDate}</Text>
        <Text style={styles.contributionDesc}>{item.description}</Text>
      </View>
      <View style={styles.contributionAmount}>
        <Text style={styles.contributionValue}>
          +{item.formattedAmount}
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {loading && !refreshing ? (
        <ActivityIndicator style={styles.loader} size="large" color="#4a6fa5" />
      ) : (
        <FlatList
          data={contributions}
          renderItem={renderContributionItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl 
              refreshing={refreshing} 
              onRefresh={onRefresh} 
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No contributions found</Text>
              <Text style={styles.emptyStateSubtext}>Your HSA contributions will appear here</Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
    paddingBottom: 80,
  },
  contributionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  contributionDetails: {
    flex: 1,
  },
  contributionDate: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  contributionDesc: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
  },
  contributionAmount: {
    alignItems: 'flex-end',
  },
  contributionValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#22c55e',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#64748b',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#94a3b8',
    textAlign: 'center',
  },
});

export default HSAContributionHistoryScreen;
