import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';

// Simulated benefit data
const benefitsData = {
  '1': {
    id: '1',
    title: 'Health Insurance',
    provider: 'BlueCross BlueShield',
    planType: 'PPO',
    policyNumber: 'HC1234567',
    coverage: 'Family',
    deductible: '$1,500',
    copay: '$25',
    status: 'Active',
    startDate: 'Jan 1, 2025',
    expires: 'Dec 31, 2025',
    documents: [
      { id: 'd1', name: 'Policy Document', date: 'Jan 1, 2025' },
      { id: 'd2', name: 'Insurance Card', date: 'Jan 1, 2025' },
    ],
    contacts: [
      { id: 'c1', name: 'Customer Service', phone: '1-************' },
      { id: 'c2', name: 'Claims Department', phone: '1-************' },
    ]
  },
  '2': {
    id: '2',
    title: 'Dental Plan',
    provider: 'Delta Dental',
    planType: 'Premier',
    policyNumber: 'DP7654321',
    coverage: 'Family',
    deductible: '$50',
    copay: '$10',
    status: 'Active',
    startDate: 'Jan 1, 2025',
    expires: 'Dec 31, 2025',
    documents: [
      { id: 'd3', name: 'Dental Policy', date: 'Jan 1, 2025' },
    ],
    contacts: [
      { id: 'c3', name: 'Dental Customer Service', phone: '**************' },
    ]
  },
  '3': {
    id: '3',
    title: 'Vision Care',
    provider: 'VSP',
    planType: 'Premium',
    policyNumber: 'VC9876543',
    coverage: 'Family',
    deductible: '$0',
    copay: '$15',
    status: 'Active',
    startDate: 'Jan 1, 2025',
    expires: 'Dec 31, 2025',
    documents: [
      { id: 'd4', name: 'Vision Policy', date: 'Jan 1, 2025' },
    ],
    contacts: [
      { id: 'c4', name: 'Vision Customer Service', phone: '**************' },
    ]
  },
  '4': {
    id: '4',
    title: '401(k)',
    provider: 'Fidelity',
    accountNumber: '401K-123456',
    contribution: '6%',
    employerMatch: '4%',
    status: 'Enrolled',
    startDate: 'Jan 1, 2025',
    documents: [
      { id: 'd5', name: '401(k) Plan Document', date: 'Jan 1, 2025' },
    ],
    contacts: [
      { id: 'c5', name: 'Retirement Services', phone: '**************' },
    ]
  }
};

const BenefitDetailsScreen = ({ route, navigation }) => {
  const { benefitId } = route.params;
  const benefit = benefitsData[benefitId];

  if (!benefit) {
    return (
      <View style={styles.container}>
        <Text>Benefit not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{benefit.title}</Text>
        <View style={styles.statusBadge}>
          <Text style={styles.statusText}>{benefit.status}</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Benefit Information</Text>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Provider</Text>
          <Text style={styles.detailValue}>{benefit.provider}</Text>
        </View>
        {benefit.planType && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Plan Type</Text>
            <Text style={styles.detailValue}>{benefit.planType}</Text>
          </View>
        )}
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>{benefit.policyNumber ? 'Policy Number' : 'Account Number'}</Text>
          <Text style={styles.detailValue}>{benefit.policyNumber || benefit.accountNumber}</Text>
        </View>
        {benefit.coverage && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Coverage</Text>
            <Text style={styles.detailValue}>{benefit.coverage}</Text>
          </View>
        )}
        {benefit.deductible && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Deductible</Text>
            <Text style={styles.detailValue}>{benefit.deductible}</Text>
          </View>
        )}
        {benefit.copay && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Copay</Text>
            <Text style={styles.detailValue}>{benefit.copay}</Text>
          </View>
        )}
        {benefit.contribution && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Contribution</Text>
            <Text style={styles.detailValue}>{benefit.contribution}</Text>
          </View>
        )}
        {benefit.employerMatch && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Employer Match</Text>
            <Text style={styles.detailValue}>{benefit.employerMatch}</Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Important Dates</Text>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Start Date</Text>
          <Text style={styles.detailValue}>{benefit.startDate}</Text>
        </View>
        {benefit.expires && (
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Expiration Date</Text>
            <Text style={styles.detailValue}>{benefit.expires}</Text>
          </View>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Documents</Text>
        {benefit.documents.map(doc => (
          <TouchableOpacity key={doc.id} style={styles.documentItem}>
            <Text style={styles.documentName}>{doc.name}</Text>
            <Text style={styles.documentDate}>{doc.date}</Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Contact Information</Text>
        {benefit.contacts.map(contact => (
          <View key={contact.id} style={styles.contactItem}>
            <Text style={styles.contactName}>{contact.name}</Text>
            <TouchableOpacity>
              <Text style={styles.contactPhone}>{contact.phone}</Text>
            </TouchableOpacity>
          </View>
        ))}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button}>
          <Text style={styles.buttonText}>Edit Benefit</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#4a6fa5',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
  },
  statusBadge: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  statusText: {
    color: '#fff',
    fontWeight: '600',
  },
  section: {
    backgroundColor: '#fff',
    marginVertical: 10,
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    color: '#757575',
    fontSize: 16,
  },
  detailValue: {
    color: '#333',
    fontWeight: '500',
    fontSize: 16,
  },
  documentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  documentName: {
    color: '#4a6fa5',
    fontSize: 16,
    fontWeight: '500',
  },
  documentDate: {
    color: '#757575',
    fontSize: 14,
  },
  contactItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contactName: {
    color: '#333',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  contactPhone: {
    color: '#4a6fa5',
    fontSize: 16,
  },
  buttonContainer: {
    margin: 16,
    marginBottom: 32,
  },
  button: {
    backgroundColor: '#4a6fa5',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BenefitDetailsScreen;
