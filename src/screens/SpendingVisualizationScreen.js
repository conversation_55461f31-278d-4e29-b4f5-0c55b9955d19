import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity, 
  ScrollView, 
  Dimensions,
  ActivityIndicator,
  RefreshControl,
  Modal,
  FlatList
} from 'react-native';
import { <PERSON><PERSON><PERSON>, Bar<PERSON>hart } from 'react-native-chart-kit';
import TransactionService from '../services/transactionService';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import { SymbolView } from 'expo-symbols';

const screenWidth = Dimensions.get('window').width;

const SpendingVisualizationScreen = () => {
  const { logout } = useSupabaseAuth();
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [years, setYears] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [yearDropdownVisible, setYearDropdownVisible] = useState(false);
  
  // State for data
  const [monthlyData, setMonthlyData] = useState({});
  const [categoryData, setCategoryData] = useState({});
  const [currentMonthlyData, setCurrentMonthlyData] = useState([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
  const [currentCategoryData, setCurrentCategoryData] = useState([]);
  const [yearlyTotal, setYearlyTotal] = useState(0);
  
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  const chartConfig = {
    backgroundColor: '#fff',
    backgroundGradientFrom: '#fff',
    backgroundGradientTo: '#fff',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(74, 111, 165, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '5',
      strokeWidth: '2',
      stroke: '#4a6fa5',
    }
  };
  
  const barChartConfig = {
    ...chartConfig,
    color: (opacity = 1) => `rgba(74, 111, 165, ${opacity})`,
  };

  // Load available years from transaction history
  const loadYears = async () => {
    try {
      const availableYears = await TransactionService.getAvailableYears();
      setYears(availableYears);
      if (availableYears.length > 0 && !availableYears.includes(selectedYear)) {
        setSelectedYear(availableYears[0]);
      }
    } catch (err) {
      console.error('Error loading years:', err);
      handleError(err);
    }
  };

  // Load spending data for all years
  const loadSpendingData = async () => {
    setLoading(true);
    setError(null);
    try {
      const yearData = {};
      const catData = {};
      
      for (const year of years) {
        // Get yearly spending data
        const yearlyData = await TransactionService.getYearlySpendingData(year);
        yearData[year] = yearlyData;
        
        // Get category spending data
        const categorySpending = await TransactionService.getCategorySpendingData(year);
        catData[year] = categorySpending;
      }
      
      setMonthlyData(yearData);
      setCategoryData(catData);
      updateCurrentData(selectedYear, yearData, catData);
    } catch (err) {
      console.error('Error loading spending data:', err);
      handleError(err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  // Update current data based on selected year
  const updateCurrentData = (year, yearData, catData) => {
    if (yearData && yearData[year]) {
      setCurrentMonthlyData(yearData[year].data || Array(12).fill(0));
      setYearlyTotal(yearData[year].total || 0);
    }
    
    if (catData && catData[year]) {
      setCurrentCategoryData(catData[year] || []);
    }
  };
  
  // Handle year selection
  const handleYearSelect = (year) => {
    setSelectedYear(year);
    updateCurrentData(year, monthlyData, categoryData);
    setYearDropdownVisible(false);
  };
  
  // Toggle year dropdown
  const toggleYearDropdown = () => {
    setYearDropdownVisible(!yearDropdownVisible);
  };
  
  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadYears();
    await loadSpendingData();
  };
  
  // Handle errors
  const handleError = (error) => {
    if (error.message && (error.message.includes('expired') || error.message.includes('authentication'))) {
      setError('Unable to load spending data. Please try again or sign out manually if the issue persists.');
    } else {
      setError('An error occurred while loading spending data.');
    }
  };
  
  // Initial load
  useEffect(() => {
    loadYears();
  }, []);
  
  // Load spending data when years change
  useEffect(() => {
    if (years.length > 0) {
      loadSpendingData();
    }
  }, [years]);
  
  // Calculate percentages for category chart
  const totalSpent = currentCategoryData.reduce((sum, item) => sum + item.amount, 0);
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4a6fa5" />
        <Text style={styles.loadingText}>Loading spending analysis...</Text>
      </View>
    );
  }
  
  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={['#4a6fa5']}
        />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Spending Analysis</Text>
        <TouchableOpacity 
          style={styles.yearDropdownButton}
          onPress={toggleYearDropdown}
        >
          <Text style={styles.yearDropdownText}>{selectedYear}</Text>
          <SymbolView name="chevron.down" size={16} color="#4a6fa5" />
        </TouchableOpacity>
      </View>

      <Modal
        visible={yearDropdownVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setYearDropdownVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setYearDropdownVisible(false)}
        >
          <View style={styles.yearDropdownContainer}>
            <FlatList
              data={years}
              keyExtractor={(item) => item}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.yearDropdownItem,
                    selectedYear === item && styles.selectedYearDropdownItem
                  ]}
                  onPress={() => handleYearSelect(item)}
                >
                  <Text 
                    style={[
                      styles.yearDropdownItemText,
                      selectedYear === item && styles.selectedYearDropdownItemText
                    ]}
                  >
                    {item}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>

      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>Total HSA Spending - {selectedYear}</Text>
        <Text style={styles.summaryAmount}>${yearlyTotal.toFixed(2)}</Text>
      </View>
      
      <View style={styles.chartCard}>
        <Text style={styles.chartTitle}>Monthly Expenses</Text>
        
        <LineChart
          data={{
            labels: months,
            datasets: [
              {
                data: currentMonthlyData,
                color: (opacity = 1) => `rgba(74, 111, 165, ${opacity})`,
                strokeWidth: 2
              }
            ]
          }}
          width={screenWidth - 32}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
          withInnerLines={false}
          withOuterLines={true}
        />
        
        <View style={styles.chartLegend}>
          <Text style={styles.legendText}>Month</Text>
        </View>
      </View>
      
      <View style={styles.chartCard}>
        <Text style={styles.chartTitle}>Spending by Category</Text>
        
        <BarChart
          data={{
            labels: currentCategoryData.map(cat => cat.name),
            datasets: [
              {
                data: currentCategoryData.map(cat => cat.amount)
              }
            ]
          }}
          width={screenWidth - 32}
          height={220}
          chartConfig={barChartConfig}
          style={styles.chart}
          withInnerLines={false}
          showValuesOnTopOfBars
          fromZero
        />
      </View>
      
      <View style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Category Breakdown</Text>
        
        {currentCategoryData.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <View style={styles.categoryHeader}>
              <Text style={styles.categoryName}>{category.name}</Text>
              <Text style={styles.categoryAmount}>${category.amount.toFixed(2)}</Text>
            </View>
            
            <View style={styles.percentageBar}>
              <View 
                style={[
                  styles.percentageFill, 
                  { 
                    width: `${(category.amount / totalSpent) * 100}%`,
                    backgroundColor: getColorByIndex(index) 
                  }
                ]} 
              />
            </View>
            
            <Text style={styles.percentageText}>
              {totalSpent > 0 ? `${((category.amount / totalSpent) * 100).toFixed(1)}%` : '0%'}
            </Text>
          </View>
        ))}
      </View>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </ScrollView>
  );
};

// Helper function to get colors for different categories
const getColorByIndex = (index) => {
  const colors = ['#4a6fa5', '#5d9cec', '#4fc1e9', '#48cfad'];
  return colors[index % colors.length];
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    padding: 20,
    backgroundColor: '#fee',
    borderRadius: 5,
    marginBottom: 10,
  },
  errorText: {
    color: '#c00',
    fontSize: 14,
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  yearDropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f1f1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  yearDropdownText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4a6fa5',
    marginRight: 6,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  yearDropdownContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '60%',
    maxHeight: 300,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  yearDropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  selectedYearDropdownItem: {
    backgroundColor: '#e6eef8',
  },
  yearDropdownItemText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  selectedYearDropdownItemText: {
    fontWeight: '600',
    color: '#4a6fa5',
  },
  summaryCard: {
    backgroundColor: '#4a6fa5',
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  summaryTitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    marginBottom: 8,
  },
  summaryAmount: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
  },
  chartCard: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 8,
  },
  chartLegend: {
    alignItems: 'center',
    marginTop: 8,
  },
  legendText: {
    color: '#757575',
    fontSize: 14,
  },
  sectionCard: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  categoryItem: {
    marginBottom: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  percentageBar: {
    height: 8,
    backgroundColor: '#f1f1f1',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  percentageFill: {
    height: '100%',
    borderRadius: 4,
  },
  percentageText: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'right',
  },
});

export default SpendingVisualizationScreen;
