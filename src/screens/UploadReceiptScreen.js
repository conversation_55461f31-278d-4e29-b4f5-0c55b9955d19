import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  TouchableOpacity, 
  ScrollView, 
  TextInput,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { StatusBar } from 'expo-status-bar';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import hsaDocumentService from '../services/hsaDocumentService';

const UploadReceiptScreen = ({ navigation }) => {
  const { user, logout } = useSupabaseAuth();
  const [receiptImage, setReceiptImage] = useState(null);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [category, setCategory] = useState('Medical');
  const [title, setTitle] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  const [ocrResults, setOcrResults] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  
  const categories = ['Medical', 'Dental', 'Vision', 'Prescription', 'Pharmacy', 'Therapy', 'Family', 'Other'];
  
  const pickImage = async () => {
    try {
      // Request permission first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your photos to upload receipts.');
        return;
      }
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1.0,
        presentationStyle: 'fullScreen',
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageAsset = result.assets[0];
        setReceiptImage(imageAsset);
        
        // Process image with OCR to auto-populate fields
        await processImageWithOCR(imageAsset);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };
  
  const takePhoto = async () => {
    try {
      // Request camera permission
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow camera access to take photos of receipts.');
        return;
      }
      
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        quality: 1.0,
        presentationStyle: 'fullScreen',
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageAsset = result.assets[0];
        setReceiptImage(imageAsset);
        
        // Process image with OCR to auto-populate fields
        await processImageWithOCR(imageAsset);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };
  
  const processImageWithOCR = async (file) => {
    try {
      setIsProcessingOCR(true);
      setModalVisible(true);
      
      // Create file object for OCR processing
      const fileObj = {
        uri: file.uri,
        type: file.type || 'image/jpeg',
        name: file.fileName || 'receipt.jpg',
      };

      // Use OCR-only processing - does NOT save to database
      const result = await hsaDocumentService.processImageOCR(fileObj);

      if (result.ocr_result && result.ocr_result.success) {
        const extractedData = result.ocr_result.extracted_data;
        
        // Auto-populate form fields with OCR results (always update with new data)
        if (extractedData.title) {
          setTitle(extractedData.title);
        }
        if (extractedData.description) {
          setDescription(extractedData.description);
        }
        if (extractedData.amount && extractedData.amount > 0) {
          setAmount(extractedData.amount.toString());
        }
        if (extractedData.category) {
          setCategory(extractedData.category);
        }
        if (extractedData.expense_date) {
          setDate(extractedData.expense_date);
        }

        setOcrResults(result.ocr_result);


      } else {
        Alert.alert(
          'OCR Processing',
          'Could not extract text from the image. Please fill out the form manually.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('OCR processing error:', error);
      Alert.alert(
        'OCR Error',
        'Failed to process the image. Please fill out the form manually.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsProcessingOCR(false);
      setModalVisible(false);
    }
  };

  const uploadDocument = async () => {
    if (!receiptImage) {
      Alert.alert('Missing Receipt', 'Please upload a receipt image first');
      return;
    }
    
    if (!amount || isNaN(parseFloat(amount))) {
      Alert.alert('Invalid Amount', 'Please enter a valid expense amount');
      return;
    }
    
    if (!description) {
      Alert.alert('Missing Description', 'Please enter a brief description');
      return;
    }

    if (!title) {
      Alert.alert('Missing Title', 'Please enter a title for this document');
      return;
    }
    
    setIsUploading(true);
    
    try {
      const documentData = {
        title,
        description,
        amount: parseFloat(amount),
        category,
        expense_date: date,
      };

      const file = {
        uri: receiptImage.uri,
        type: receiptImage.type || 'image/jpeg',
        name: receiptImage.fileName || `receipt_${Date.now()}.jpg`,
      };

      const result = await hsaDocumentService.uploadDocument(file, documentData);
      
      if (result.error) {
        Alert.alert('Upload Failed', result.error || 'Failed to upload document');
        return;
      }

      // Handle OCR results if available
      if (result.ocr_result) {
        setOcrResults(result.ocr_result);
        console.log('OCR Results:', result.ocr_result);
      }

      Alert.alert(
        'Document Uploaded',
        'Your HSA document has been uploaded successfully',
        [
          { 
            text: 'OK', 
            onPress: () => navigation.navigate('HSAManagement', { refresh: true }) 
          }
        ]
      );
      
      // Reset form
      setReceiptImage(null);
      setAmount('');
      setDescription('');
      setTitle('');
      setDate(new Date().toISOString().split('T')[0]);
      setCategory('Medical');
      setOcrResults(null);
      
    } catch (error) {
      console.error('Upload document error:', error);
      
      Alert.alert(
        'Upload Failed', 
        error.message || 'An unexpected error occurred while uploading the document'
      );
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <KeyboardAvoidingView 
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.container}
        pointerEvents={isProcessingOCR ? 'none' : 'auto'}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Upload Receipt</Text>
          <Text style={styles.subtitle}>Record your HSA eligible expenses</Text>
        </View>
        
        <View style={styles.uploadSection}>
          {receiptImage ? (
            <View style={styles.imagePreviewContainer}>
              <Image 
                source={{ uri: receiptImage.uri }} 
                style={styles.imagePreview} 
              />
              <TouchableOpacity 
                style={styles.changeImageButton}
                onPress={pickImage}
              >
                <Text style={styles.changeImageText}>Change Image</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.uploadOptions}>
              <TouchableOpacity 
                style={styles.uploadOption}
                onPress={takePhoto}
              >
                <Text style={styles.uploadOptionIcon}>📷</Text>
                <Text style={styles.uploadOptionText}>Take Photo</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.uploadOption}
                onPress={pickImage}
              >
                <Text style={styles.uploadOptionIcon}>🖼️</Text>
                <Text style={styles.uploadOptionText}>Choose Image</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
        
        <View style={styles.formSection}>
          <Text style={styles.formLabel}>Title</Text>
          <TextInput
            style={styles.input}
            value={title}
            onChangeText={setTitle}
            placeholder="e.g., Doctor visit co-pay"
          />
          
          <Text style={styles.formLabel}>Expense Amount ($)</Text>
          <TextInput
            style={styles.input}
            value={amount}
            onChangeText={setAmount}
            placeholder="0.00"
            keyboardType="decimal-pad"
            returnKeyType="done"
          />
          
          <Text style={styles.formLabel}>Description</Text>
          <TextInput
            style={styles.input}
            value={description}
            onChangeText={setDescription}
            placeholder="e.g., Doctor visit co-pay"
          />
          
          <Text style={styles.formLabel}>Date</Text>
          <View style={styles.dateContainer}>
            <TextInput
              style={[styles.input, { flex: 1 }]}
              value={date}
              onChangeText={setDate}
              placeholder="YYYY-MM-DD"
            />
            <TouchableOpacity 
              style={styles.todayButton}
              onPress={() => setDate(new Date().toISOString().split('T')[0])}
            >
              <Text style={styles.todayButtonText}>Today</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={styles.formLabel}>Category</Text>
          <View style={styles.categoryContainer}>
            {categories.map((cat) => (
              <TouchableOpacity
                key={cat}
                style={[
                  styles.categoryButton,
                  category === cat && styles.selectedCategory
                ]}
                onPress={() => setCategory(cat)}
              >
                <Text 
                  style={[
                    styles.categoryText,
                    category === cat && styles.selectedCategoryText
                  ]}
                >
                  {cat}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <TouchableOpacity 
          style={[
            styles.submitButton,
            (isUploading || isProcessingOCR) && styles.disabledButton
          ]}
          onPress={uploadDocument}
          disabled={isUploading || isProcessingOCR}
        >
          <Text style={styles.submitButtonText}>
            {isProcessingOCR ? 'Processing...' : isUploading ? 'Uploading...' : 'Upload HSA Document'}
          </Text>
        </TouchableOpacity>
        

        
        <StatusBar style="auto" />
      </ScrollView>

      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(!modalVisible);
        }}
      >
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <View style={styles.modalIcon}>
              <ActivityIndicator size="large" color="#4a6fa5" />
            </View>
            <Text style={styles.modalTitle}>Processing Receipt</Text>
            <Text style={styles.modalSubtitle}>Extracting information with AI...</Text>
          </View>
        </View>
      </Modal>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#757575',
    marginTop: 4,
  },
  uploadSection: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    alignItems: 'center',
  },
  uploadOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingVertical: 30,
  },
  uploadOption: {
    alignItems: 'center',
    backgroundColor: 'transparent',
    padding: 30,
    borderRadius: 12,
    width: '45%',
  },
  uploadOptionIcon: {
    fontSize: 40,
    marginBottom: 12,
  },
  uploadOptionText: {
    fontSize: 14,
    color: '#4a6fa5',
  },
  imagePreviewContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 10,
  },
  imagePreview: {
    width: '100%',
    height: 400,
    borderRadius: 12,
    marginBottom: 10,
    resizeMode: 'contain',
  },
  changeImageButton: {
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  changeImageText: {
    color: '#4a6fa5',
    fontSize: 16,
  },
  formSection: {
    backgroundColor: '#fff',
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  formLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  todayButton: {
    backgroundColor: '#4a6fa5',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginLeft: 10,
  },
  todayButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#f1f1f1',
    marginRight: 8,
    marginBottom: 8,
  },
  selectedCategory: {
    backgroundColor: '#4a6fa5',
  },
  categoryText: {
    fontSize: 14,
    color: '#757575',
  },
  selectedCategoryText: {
    color: '#ffffff',
  },
  submitButton: {
    backgroundColor: '#4a6fa5',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 32,
  },
  disabledButton: {
    opacity: 0.5,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalView: {
    margin: 20,
    backgroundColor: "white",
    borderRadius: 20,
    padding: 35,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '80%',
  },
  modalIcon: {
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#757575',
    marginBottom: 20,
  },
});

export default UploadReceiptScreen;
