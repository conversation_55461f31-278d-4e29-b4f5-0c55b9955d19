import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
  Alert
} from 'react-native';
import transactionService from '../services/transactionService';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';

const HSAAllTransactionsScreen = ({ navigation }) => {
  const { logout } = useSupabaseAuth();
  const [monthlyTransactions, setMonthlyTransactions] = useState([]);
  const [recentItems, setRecentItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadTransactions = async () => {
    try {
      // Use getAllTransactions instead of getRecentTransactions to fix 400 error
      const result = await transactionService.getAllTransactions();
      
      if (result.success && Array.isArray(result.transactions)) {
        // Set all transactions to both monthly and recent arrays
        setMonthlyTransactions(result.transactions || []);
        // Take just the first few transactions for recent items
        setRecentItems(result.transactions.slice(0, 3) || []);
        console.log('Loaded all transactions:', result.transactions.length);
      } else {
        console.error('Failed to load transactions:', result);
        setMonthlyTransactions([]);
        setRecentItems([]);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      if (error.response?.status === 401) {
        Alert.alert('Session Expired', 'Please try again or check your connection.');
      } else {
        Alert.alert('Error', `Failed to load transactions: ${error.message}`);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadTransactions();
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    loadTransactions();
  };

  const sortedTransactions = [...monthlyTransactions].sort((a, b) => {
    const dateA = new Date(a.date || a.expense_date || 0);
    const dateB = new Date(b.date || b.expense_date || 0);
    return dateB - dateA;
  });

  const renderTransactionItem = ({ item }) => {
    if (!item) return null;
    
    const amount = typeof item.amount === 'number' ? item.amount : parseFloat(item.amount || '0');
    const vendor = item.vendor || 'Unknown Vendor';
    const date = item.date ? new Date(item.date) : new Date();

    return (
      <TouchableOpacity 
        style={styles.transactionCard}
        onPress={() => navigation.navigate('HSADocumentDetail', { documentId: item.id })}
      >
        <View style={styles.transactionHeader}>
          <Text style={styles.vendorName}>{vendor}</Text>
          <Text style={styles.amount}>-${amount.toFixed(2)}</Text>
        </View>
        <Text style={styles.date}>{date.toLocaleDateString()}</Text>
      </TouchableOpacity>
    );
  };

  const renderRecentItem = (item) => (
    <TouchableOpacity 
      style={styles.recentItemCard}
      onPress={() => navigation.navigate('HSADocumentDetail', { documentId: item.id })}
    >
      <View style={styles.transactionHeader}>
        <Text style={styles.vendorName}>{item.vendor || 'Unknown Vendor'}</Text>
        <Text style={styles.amount}>-${parseFloat(item.amount || '0').toFixed(2)}</Text>
      </View>
      <Text style={styles.date}>{new Date(item.date).toLocaleDateString()}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {loading ? (
        <ActivityIndicator size="large" color="#0000ff" style={styles.loader} />
      ) : (
        <View style={styles.content}>
          <View style={styles.recentItemsSection}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <View style={styles.recentItemsContainer}>
              {recentItems.map((item, index) => (
                <View key={item.id || index} style={styles.recentItemWrapper}>
                  {renderRecentItem(item)}
                </View>
              ))}
            </View>
          </View>

          <View style={styles.monthlySection}>
            <Text style={styles.sectionTitle}>All Transactions</Text>
            <FlatList
              data={sortedTransactions}
              renderItem={renderTransactionItem}
              keyExtractor={(item, index) => item.id || `transaction-${index}`}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                />
              }
              ListEmptyComponent={
                <Text style={styles.emptyText}>No transactions found</Text>
              }
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f4f7fc',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  recentItemsSection: {
    marginBottom: 24,
  },
  recentItemsContainer: {
    flexDirection: 'column',
  },
  recentItemWrapper: {
    marginBottom: 12,
  },
  recentItemCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  monthlySection: {
    flex: 1,
  },
  vendorName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1e293b',
    flex: 1,
    letterSpacing: -0.3,
  },
  amount: {
    fontSize: 18,
    fontWeight: '800',
    color: '#ef4444',
    letterSpacing: -0.3,
  },
  date: {
    fontSize: 14,
    color: '#666',
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
  },
  transactionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 18,
    marginBottom: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#f0f4fa',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
  },
  transactionDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  value: {
    fontSize: 14,
    color: '#334155',
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 6,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  listContent: {
    padding: 16,
  }
});

export default HSAAllTransactionsScreen;
