import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { useSupabaseAuth } from '../context/SupabaseAuthContext';
import hsaDocumentService from '../services/hsaDocumentService';

const HSADocumentsScreen = ({ navigation, route }) => {
  const { logout } = useSupabaseAuth();
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadDocuments = async () => {
    try {
      const documents = await hsaDocumentService.getDocuments();
      setDocuments(documents);
    } catch (error) {
      console.error('Load documents error:', error);
      // Show error message instead of forcing logout
      if (error.message && error.message.includes('session has expired')) {
        Alert.alert('Session Error', 'Unable to load documents. Please try again or sign out manually if the issue persists.');
      } else {
        Alert.alert('Error', 'Failed to load documents');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadDocuments();
  };

  useFocusEffect(
    useCallback(() => {
      loadDocuments();
    }, [])
  );

  // Refresh when coming back from upload screen
  useEffect(() => {
    if (route.params?.refresh) {
      loadDocuments();
    }
  }, [route.params?.refresh]);

  const handleDeleteDocument = async (documentId) => {
    Alert.alert(
      'Delete Document',
      'Are you sure you want to delete this document?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await hsaDocumentService.deleteDocument(documentId);
              if (result.success) {
                Alert.alert('Success', 'Document deleted successfully');
                loadDocuments(); // Refresh the list
              } else {
                if (result.error === 'Unauthorized') {
                  Alert.alert('Session Error', 'Unable to delete document. Please try again or sign out manually if the issue persists.');
                } else {
                  Alert.alert('Error', result.error || 'Failed to delete document');
                }
              }
            } catch (error) {
              console.error('Delete error:', error);
              if (error.message && error.message.includes('session has expired')) {
                Alert.alert('Session Error', 'Unable to delete document. Please try again or sign out manually if the issue persists.');
              } else {
                Alert.alert('Error', 'Failed to delete document');
              }
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const formatAmount = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return '#28a745';
      case 'rejected':
        return '#dc3545';
      case 'pending':
      default:
        return '#ffc107';
    }
  };

  const renderDocumentItem = ({ item }) => (
    <TouchableOpacity
      style={styles.documentCard}
      onPress={() => navigation.navigate('HSADocumentDetail', { documentId: item.id })}
    >
      <View style={styles.documentHeader}>
        <View style={styles.documentInfo}>
          <Text style={styles.documentTitle}>{item.title}</Text>
          <Text style={styles.documentDescription}>{item.description}</Text>
          <View style={styles.documentMeta}>
            <Text style={styles.metaText}>Amount: {formatAmount(item.amount)}</Text>
            <Text style={styles.metaText}>Category: {item.category}</Text>
            <Text style={styles.metaText}>Date: {formatDate(item.expense_date)}</Text>
          </View>
        </View>
      </View>
      
      {item.file_url && (
        <Image source={{ uri: item.file_url }} style={styles.documentImage} />
      )}
      
      <View style={styles.documentFooter}>
        <Text style={styles.uploadDate}>
          Uploaded: {formatDate(item.created_at)}
        </Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={(e) => {
            e.stopPropagation();
            handleDeleteDocument(item.id);
          }}
        >
          <Text style={styles.deleteButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading documents...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>HSA Documents</Text>
        <TouchableOpacity
          style={styles.uploadButton}
          onPress={() => navigation.navigate('UploadReceipt')}
        >
          <Text style={styles.uploadButtonText}>+ Upload</Text>
        </TouchableOpacity>
      </View>

      {documents.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No documents uploaded yet</Text>
          <TouchableOpacity
            style={styles.emptyUploadButton}
            onPress={() => navigation.navigate('UploadReceipt')}
          >
            <Text style={styles.emptyUploadButtonText}>Upload Your First Document</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={documents}
          renderItem={renderDocumentItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  uploadButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  listContainer: {
    padding: 16,
  },
  documentCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  documentMeta: {
    gap: 2,
  },
  metaText: {
    fontSize: 12,
    color: '#888',
  },
  documentActions: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  documentImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
    resizeMode: 'cover',
  },
  documentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  uploadDate: {
    fontSize: 12,
    color: '#888',
  },
  deleteButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  emptyUploadButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyUploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default HSADocumentsScreen;
