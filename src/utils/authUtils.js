import { useAuth } from '../context/AuthContext';

/**
 * Higher-order function that ensures a fresh token before executing critical operations
 * Use this wrapper for important API calls like uploading receipts, viewing transactions, etc.
 */
export const withFreshToken = (operation) => {
  return async (...args) => {
    const { refreshTokenIfNeeded } = useAuth();
    
    try {
      // Ensure token is fresh before proceeding
      console.log('🔐 Ensuring fresh token before critical operation...');
      const tokenRefreshed = await refreshTokenIfNeeded();
      
      if (!tokenRefreshed) {
        console.warn('🔐 Could not refresh token for critical operation');
        throw new Error('Authentication token could not be refreshed');
      }
      
      // Execute the operation with fresh token
      console.log('🔐 Executing operation with fresh token');
      return await operation(...args);
      
    } catch (error) {
      console.error('🔐 Critical operation failed:', error);
      throw error;
    }
  };
};

/**
 * Hook to get a function that ensures fresh tokens before operations
 */
export const useFreshTokenOperation = () => {
  const { refreshTokenIfNeeded } = useAuth();
  
  return async (operation, ...args) => {
    try {
      console.log('🔐 Ensuring fresh token before operation...');
      const tokenRefreshed = await refreshTokenIfNeeded();
      
      if (!tokenRefreshed) {
        console.warn('🔐 Could not refresh token for operation');
        throw new Error('Authentication token could not be refreshed');
      }
      
      console.log('🔐 Executing operation with fresh token');
      return await operation(...args);
      
    } catch (error) {
      console.error('🔐 Operation with fresh token failed:', error);
      throw error;
    }
  };
};

/**
 * Utility to check token expiry status
 */
export const useTokenStatus = () => {
  const { tokenExpiry, refreshToken } = useAuth();
  
  const getTokenStatus = () => {
    if (!tokenExpiry) {
      return {
        status: 'unknown',
        message: 'Token expiry information not available',
        minutesUntilExpiry: null
      };
    }
    
    const timeUntilExpiry = tokenExpiry - Date.now();
    const minutesUntilExpiry = Math.round(timeUntilExpiry / 60000);
    
    if (timeUntilExpiry <= 0) {
      return {
        status: 'expired',
        message: 'Token has expired',
        minutesUntilExpiry: 0
      };
    } else if (timeUntilExpiry < 5 * 60 * 1000) { // Less than 5 minutes
      return {
        status: 'expiring_soon',
        message: `Token expires in ${minutesUntilExpiry} minutes`,
        minutesUntilExpiry
      };
    } else {
      return {
        status: 'valid',
        message: `Token valid for ${minutesUntilExpiry} minutes`,
        minutesUntilExpiry
      };
    }
  };
  
  const hasRefreshCapability = !!refreshToken;
  
  return {
    getTokenStatus,
    hasRefreshCapability,
    tokenExpiry
  };
};
