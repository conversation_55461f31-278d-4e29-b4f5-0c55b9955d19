import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { AppState } from 'react-native';
import config from '../config/config';

// Storage keys
const SESSION_KEY = 'supabase_session';
const USER_KEY = 'user_data';

// API configuration
const API_URL = config.API_BASE_URL.replace('/api', '');

// Create the context
const SupabaseAuthContext = createContext();

export const SupabaseAuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Secure storage helpers
  const storeSecureData = async (key, value) => {
    try {
      await SecureStore.setItemAsync(key, JSON.stringify(value));
      console.log(`✅ Stored ${key} securely`);
    } catch (error) {
      console.error(`❌ Error storing ${key} securely:`, error);
      // Fallback to AsyncStorage
      try {
        await AsyncStorage.setItem(key, JSON.stringify(value));
        console.log(`✅ Stored ${key} in AsyncStorage as fallback`);
      } catch (fallbackError) {
        console.error(`❌ Error storing ${key} in AsyncStorage fallback:`, fallbackError);
      }
    }
  };

  const getSecureData = async (key) => {
    try {
      const value = await SecureStore.getItemAsync(key);
      if (value) {
        console.log(`✅ Retrieved ${key} from secure storage`);
        return JSON.parse(value);
      }
    } catch (error) {
      console.error(`❌ Error retrieving ${key} from secure storage:`, error);
    }
    
    // Fallback to AsyncStorage
    try {
      const value = await AsyncStorage.getItem(key);
      if (value) {
        console.log(`✅ Retrieved ${key} from AsyncStorage fallback`);
        return JSON.parse(value);
      }
    } catch (fallbackError) {
      console.error(`❌ Error retrieving ${key} from AsyncStorage fallback:`, fallbackError);
    }
    
    return null;
  };

  const removeSecureData = async (key) => {
    try {
      await SecureStore.deleteItemAsync(key);
      console.log(`✅ Removed ${key} from secure storage`);
    } catch (error) {
      console.error(`❌ Error removing ${key} from secure storage:`, error);
    }
    
    try {
      await AsyncStorage.removeItem(key);
      console.log(`✅ Removed ${key} from AsyncStorage fallback`);
    } catch (fallbackError) {
      console.error(`❌ Error removing ${key} from AsyncStorage fallback:`, fallbackError);
    }
  };

  // Initialize authentication on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  // Monitor app state for session refresh
  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      if (nextAppState === 'active' && session) {
        console.log('🔄 App resumed, checking session validity');
        await refreshSessionIfNeeded();
      }
    };
    
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [session]);

  const initializeAuth = async () => {
    try {
      console.log('🔐 Initializing authentication...');
      setLoading(true);

      // Try to restore session from storage
      const storedSession = await getSecureData(SESSION_KEY);
      const storedUser = await getSecureData(USER_KEY);

      if (storedSession && storedUser) {
        console.log('🔐 Found stored session, validating...');
        
        // Check if session is still valid
        const isValid = await validateSession(storedSession);
        if (isValid) {
          console.log('✅ Session is valid, restoring authentication');
          setSession(storedSession);
          setUser(storedUser);
          setIsAuthenticated(true);
        } else {
          console.log('🔄 Session expired, attempting refresh...');
          const refreshed = await refreshSession(storedSession);
          if (!refreshed) {
            console.log('❌ Session refresh failed, clearing stored data');
            await clearStoredAuth();
          }
        }
      } else {
        console.log('🔐 No stored session found');
      }
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      await clearStoredAuth();
    } finally {
      setLoading(false);
    }
  };

  const validateSession = async (sessionData) => {
    if (!sessionData || !sessionData.access_token) {
      return false;
    }

    // Check if token is expired
    const expiresAt = sessionData.expires_at;
    if (expiresAt && Date.now() / 1000 > expiresAt - 60) { // 60 second buffer
      console.log('🔄 Token is expired or expires soon');
      return false;
    }

    return true;
  };

  const refreshSession = async (currentSession) => {
    if (!currentSession?.refresh_token) {
      console.error('❌ No refresh token available');
      return false;
    }

    try {
      console.log('🔄 Refreshing session with refresh token...');
      
      const response = await fetch(`${API_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: currentSession.refresh_token
        })
      });

      if (!response.ok) {
        console.error('❌ Refresh token request failed:', response.status);
        return false;
      }

      const data = await response.json();
      
      if (data.error) {
        console.error('❌ Refresh token error:', data.error);
        return false;
      }

      // Update session with new tokens
      const newSession = {
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        expires_at: data.expires_at,
        expires_in: data.expires_in,
        token_type: data.token_type || 'bearer',
        user: data.user
      };

      console.log('✅ Session refreshed successfully');
      
      // Store new session
      await storeSecureData(SESSION_KEY, newSession);
      await storeSecureData(USER_KEY, data.user);
      
      // Update state
      setSession(newSession);
      setUser(data.user);
      setIsAuthenticated(true);

      return true;
    } catch (error) {
      console.error('❌ Error refreshing session:', error);
      return false;
    }
  };

  const refreshSessionIfNeeded = async () => {
    if (!session) return;

    const isValid = await validateSession(session);
    if (!isValid) {
      console.log('🔄 Session needs refresh');
      await refreshSession(session);
    }
  };

  const proactiveRefresh = async () => {
    if (!session?.access_token) return;
    
    const isValid = await validateSession(session);
    if (!isValid) {
      console.log('🔄 Session needs refresh');
      await refreshSession(session);
    }
  };

  const login = async (email, password) => {
    try {
      console.log('🔐 Attempting login...');
      setLoading(true);

      const response = await fetch(`${API_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (!response.ok || data.error) {
        console.error('❌ Login failed:', data.error || 'Unknown error');
        return { success: false, error: data.error || 'Login failed' };
      }

      // Create session object
      const newSession = {
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        expires_at: data.expires_at,
        expires_in: data.expires_in,
        token_type: data.token_type || 'bearer',
        user: data.user
      };

      console.log('✅ Login successful');

      // Store session and user data
      await storeSecureData(SESSION_KEY, newSession);
      await storeSecureData(USER_KEY, data.user);

      // Update state
      setSession(newSession);
      setUser(data.user);
      setIsAuthenticated(true);

      return { success: true, user: data.user };
    } catch (error) {
      console.error('❌ Login error:', error);
      return { success: false, error: error.message || 'Network error' };
    } finally {
      setLoading(false);
    }
  };

  const register = async (email, password, userData = {}) => {
    try {
      console.log('🔐 Attempting registration...');
      setLoading(true);

      const response = await fetch(`${API_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          password,
          ...userData
        })
      });

      const data = await response.json();

      if (!response.ok || data.error) {
        console.error('❌ Registration failed:', data.error || 'Unknown error');
        return { success: false, error: data.error || 'Registration failed' };
      }

      console.log('✅ Registration successful');
      return { success: true, message: 'Registration successful' };
    } catch (error) {
      console.error('❌ Registration error:', error);
      return { success: false, error: error.message || 'Network error' };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log('🔐 Logging out...');
      setLoading(true);

      // Clear stored data
      await clearStoredAuth();

      // Clear state
      setSession(null);
      setUser(null);
      setIsAuthenticated(false);

      console.log('✅ Logout successful');
    } catch (error) {
      console.error('❌ Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearStoredAuth = async () => {
    await removeSecureData(SESSION_KEY);
    await removeSecureData(USER_KEY);
  };

  // Auto-refresh session periodically (every 45 minutes)
  useEffect(() => {
    if (!session || !isAuthenticated) return;

    const refreshInterval = setInterval(async () => {
      console.log('🔄 Periodic session refresh check...');
      await proactiveRefresh();
    }, 45 * 60 * 1000); // 45 minutes

    return () => clearInterval(refreshInterval);
  }, [session, isAuthenticated]);

  const value = {
    user,
    session,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshSession: () => refreshSession(session),
  };

  return (
    <SupabaseAuthContext.Provider value={value}>
      {children}
    </SupabaseAuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useSupabaseAuth = () => {
  const context = useContext(SupabaseAuthContext);
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return context;
};

export default SupabaseAuthContext;
