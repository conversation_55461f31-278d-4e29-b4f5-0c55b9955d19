import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { AppState } from 'react-native';
import axios from 'axios';
import config from '../config/config';

// Secure storage keys for sensitive data
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'user_data';
const SESSION_KEY = 'supabase_session';
const TOKEN_EXPIRY_KEY = 'token_expiry';

// API configuration
const API_URL = config.API_BASE_URL.replace('/api', '');

// Token refresh timing constants
const TOKEN_REFRESH_INTERVAL = 50 * 60 * 1000; // 50 minutes
const TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before expiry

// Create the context
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [refreshToken, setRefreshToken] = useState(null);
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [tokenExpiry, setTokenExpiry] = useState(null);

  // Secure storage helper functions using expo-secure-store
  const storeSecureData = async (key, value) => {
    try {
      await SecureStore.setItemAsync(key, value);
      console.log(`✅ Stored ${key} securely`);
    } catch (error) {
      console.error(`❌ Error storing ${key} securely:`, error);
      // Fallback to AsyncStorage if SecureStore fails
      try {
        await AsyncStorage.setItem(key, value);
        console.log(`✅ Stored ${key} in AsyncStorage as fallback`);
      } catch (fallbackError) {
        console.error(`❌ Error storing ${key} in AsyncStorage fallback:`, fallbackError);
      }
    }
  };

  const getSecureData = async (key) => {
    try {
      const value = await SecureStore.getItemAsync(key);
      if (value) {
        console.log(`✅ Retrieved ${key} from secure storage`);
        return value;
      }
    } catch (error) {
      console.error(`❌ Error retrieving ${key} from secure storage:`, error);
    }
    
    // Fallback to AsyncStorage
    try {
      const value = await AsyncStorage.getItem(key);
      if (value) {
        console.log(`✅ Retrieved ${key} from AsyncStorage fallback`);
        return value;
      }
    } catch (fallbackError) {
      console.error(`❌ Error retrieving ${key} from AsyncStorage fallback:`, fallbackError);
    }
    
    return null;
  };

  const removeSecureData = async (key) => {
    try {
      await SecureStore.deleteItemAsync(key);
      console.log(`✅ Removed ${key} from secure storage`);
    } catch (error) {
      console.error(`❌ Error removing ${key} from secure storage:`, error);
    }
    
    // Also remove from AsyncStorage fallback
    try {
      await AsyncStorage.removeItem(key);
      console.log(`✅ Removed ${key} from AsyncStorage fallback`);
    } catch (fallbackError) {
      console.error(`❌ Error removing ${key} from AsyncStorage fallback:`, fallbackError);
    }
  };

  // Initialize authentication on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  // Set up global axios defaults and interceptors
  useEffect(() => {
    if (token) {
      console.log('🔐 Setting global axios Authorization header with token');
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
  }, [token]);

  // Proactive token refresh timer - refresh before expiration
  useEffect(() => {
    if (token && refreshToken) {
      console.log('🔄 Setting up proactive token refresh timer (every 50 minutes)');
      
      const refreshInterval = setInterval(async () => {
        console.log('🔄 Proactively refreshing token before expiration');
        const refreshed = await refreshAuthToken();
        if (!refreshed) {
          console.warn('🔄 Proactive token refresh failed - token may expire soon');
        }
      }, TOKEN_REFRESH_INTERVAL);
      
      return () => {
        console.log('🔄 Clearing proactive token refresh timer');
        clearInterval(refreshInterval);
      };
    }
  }, [token, refreshToken]);

  // App state monitoring - refresh token when app becomes active
  useEffect(() => {
    const handleAppStateChange = async (nextAppState) => {
      if (nextAppState === 'active' && token && refreshToken) {
        console.log('🔄 App resumed from background, checking token validity');
        
        // Check if token is close to expiry or already expired
        if (tokenExpiry) {
          const timeUntilExpiry = tokenExpiry - Date.now();
          if (timeUntilExpiry < TOKEN_EXPIRY_BUFFER) {
            console.log('🔄 Token expires soon, refreshing proactively');
            await refreshAuthToken();
          } else {
            console.log(`🔄 Token still valid for ${Math.round(timeUntilExpiry / 60000)} minutes`);
          }
        } else {
          // If we don't have expiry info, refresh to be safe
          console.log('🔄 No expiry info available, refreshing token to be safe');
          await refreshAuthToken();
        }
      }
    };
    
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [token, refreshToken, tokenExpiry]);

  // Set up axios interceptors for automatic token handling
  useEffect(() => {
    let refreshInProgress = false;

    // Request interceptor - add token to all requests except refresh
    const requestInterceptor = axios.interceptors.request.use(
      async (config) => {
        try {
          // Skip adding token to refresh endpoint to prevent circular dependency
          if (config.url && config.url.includes('/api/auth/refresh')) {
            console.log('🔐 Skipping token attachment for refresh endpoint');
            return config;
          }

          // Always get the latest token from storage
          const currentToken = await getSecureData(TOKEN_KEY);
          
          if (currentToken) {
            console.log(`🔐 Adding token to request: ${config.url}`);
            config.headers.Authorization = `Bearer ${currentToken}`;
          } else {
            console.log('🔐 No token available for request');
          }
          
          return config;
        } catch (error) {
          console.error('🔐 Error in request interceptor:', error);
          return config;
        }
      },
      (error) => {
        console.error('🔐 Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor - handle 401 errors with token refresh
    // Track consecutive refresh failures
    let consecutiveRefreshFailures = 0;
    const MAX_REFRESH_FAILURES = 3;
    
    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        // Reset failure counter on successful responses
        consecutiveRefreshFailures = 0;
        return response;
      },
      async (error) => {
        const originalRequest = error.config;
        
        // Check if this is a 401 error and we haven't already tried to refresh for this request
        if (error.response?.status === 401 && !originalRequest._retry && !refreshInProgress) {
          // Skip refresh attempts for the refresh endpoint itself
          if (originalRequest.url && originalRequest.url.includes('/api/auth/refresh')) {
            console.log('🔐 Refresh endpoint failed - not attempting recursive refresh');
            consecutiveRefreshFailures++;
            console.log(`🔐 Consecutive refresh failures: ${consecutiveRefreshFailures}`);
            
            // If we've failed multiple times, redirect to auth reset screen
            if (consecutiveRefreshFailures >= MAX_REFRESH_FAILURES) {
              console.log('🔐 Too many consecutive refresh failures, redirecting to auth reset');
              // Use setTimeout to avoid state updates during render
              setTimeout(() => {
                // Navigate to AuthResetScreen if available in navigation
                if (global.navigationRef?.current?.navigate) {
                  global.navigationRef.current.navigate('AuthResetScreen');
                }
              }, 500);
            }
            return Promise.reject(error);
          }

          console.log('🔐 Received 401, attempting token refresh...');
          originalRequest._retry = true;
          refreshInProgress = true;

          try {
            const refreshed = await refreshAuthToken();
            if (refreshed) {
              console.log('🔐 Token refreshed successfully, retrying request');
              consecutiveRefreshFailures = 0; // Reset counter on success
              const newToken = await getSecureData(TOKEN_KEY);
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return axios(originalRequest);
            } else {
              console.log('🔐 Token refresh failed');
              consecutiveRefreshFailures++;
              console.log(`🔐 Consecutive refresh failures: ${consecutiveRefreshFailures}`);
              
              // If we've failed multiple times, redirect to auth reset screen
              if (consecutiveRefreshFailures >= MAX_REFRESH_FAILURES) {
                console.log('🔐 Too many consecutive refresh failures, redirecting to auth reset');
                // Use setTimeout to avoid state updates during render
                setTimeout(() => {
                  // Navigate to AuthResetScreen if available in navigation
                  if (global.navigationRef?.current?.navigate) {
                    global.navigationRef.current.navigate('AuthResetScreen');
                  }
                }, 500);
              }
              return Promise.reject(error);
            }
          } catch (refreshError) {
            console.error('🔐 Error during token refresh:', refreshError);
            consecutiveRefreshFailures++;
            return Promise.reject(error);
          } finally {
            refreshInProgress = false;
          }
        }
        
        return Promise.reject(error);
      }
    );

    // Cleanup function to remove interceptors
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [refreshAuthToken, getSecureData]);

  // Enhanced secure token storage with expiry tracking
  const storeSecureToken = useCallback(async (accessToken, refreshToken, expiryTime = null) => {
    try {
      // Calculate expiry time if not provided (assume 1 hour lifespan)
      const expiry = expiryTime || (Date.now() + 3540000); // 59 minutes from now
      
      await storeSecureData(TOKEN_KEY, accessToken);
      if (refreshToken) {
        await storeSecureData(REFRESH_TOKEN_KEY, refreshToken);
      }
      await storeSecureData(TOKEN_EXPIRY_KEY, expiry.toString());
      setTokenExpiry(expiry);
      
      console.log('🔐 Tokens stored securely with expiry tracking');
      console.log(`🔐 Token expires at: ${new Date(expiry).toLocaleString()}`);
    } catch (error) {
      console.error('🔐 Error storing tokens securely:', error);
    }
  }, []);

  const getSecureToken = useCallback(async () => {
    return await getSecureData(TOKEN_KEY);
  }, []);

  const getSecureRefreshToken = useCallback(async () => {
    return await getSecureData(REFRESH_TOKEN_KEY);
  }, []);

  const getTokenExpiry = useCallback(async () => {
    const expiryStr = await getSecureData(TOKEN_EXPIRY_KEY);
    return expiryStr ? parseInt(expiryStr) : null;
  }, []);

  const clearSecureTokens = useCallback(async () => {
    try {
      await removeSecureData(TOKEN_KEY);
      await removeSecureData(REFRESH_TOKEN_KEY);
      await removeSecureData(SESSION_KEY);
      await removeSecureData(TOKEN_EXPIRY_KEY);
      setTokenExpiry(null);
    } catch (error) {
      console.error(' Error clearing secure tokens:', error);
    }
  }, []);

  // Enhanced refresh token function with better error handling and logging
  const refreshAuthToken = async () => {
    console.log('🔐 Attempting to refresh auth token...');
    try {
      // Get the refresh token from secure storage
      const refreshToken = await getSecureData(REFRESH_TOKEN_KEY);
      
      if (!refreshToken) {
        console.log('🔐 No refresh token found - authentication state is invalid');
        return false;
      }
      
      console.log(`🔐 Using refresh token: ${refreshToken.substring(0, 5)}...${refreshToken.substring(refreshToken.length - 5)}`);
      
      // Use fetch instead of axios to avoid interceptor loop
      console.log(`🔐 Calling refresh endpoint: ${API_URL}/api/auth/refresh`);
      const response = await fetch(`${API_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });
      
      console.log(`🔐 Refresh response status: ${response.status}`);
      
      // Get response body
      const data = await response.json();
      console.log('🔐 Refresh response body:', JSON.stringify(data));
      
      if (!response.ok) {
        console.log('🔐 Token refresh failed:', data.error || 'Unknown error');
        return false;
      }
      
      if (!data.access_token || !data.refresh_token) {
        console.log('🔐 Invalid token response - missing tokens:', JSON.stringify(data));
        return false;
      }
      
      // Store the new tokens
      await storeSecureData(TOKEN_KEY, data.access_token);
      await storeSecureData(REFRESH_TOKEN_KEY, data.refresh_token);
      
      // Calculate and store token expiry (default to 1 hour if not provided)
      const expiresIn = 3600; // 1 hour in seconds
      const expiryTime = new Date().getTime() + expiresIn * 1000;
      await storeSecureData(TOKEN_EXPIRY_KEY, expiryTime.toString());
      
      // Update axios default headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${data.access_token}`;
      
      // Update auth state in context
      setToken(data.access_token);
      setRefreshToken(data.refresh_token);
      setTokenExpiry(expiryTime);
      setIsAuthenticated(true);
      
      console.log('🔐 Token refreshed successfully');
      console.log(`🔐 New token expires at: ${new Date(expiryTime).toLocaleString()}`);
      return true;
    } catch (error) {
      console.error('🔐 Token refresh failed:', error);
      
      // Log more details about the error
      if (error.response) {
        console.error('🔐 Error response data:', error.response.data);
        console.error('🔐 Error response status:', error.response.status);
      } else if (error.request) {
        console.error('🔐 Error request:', error.request);
      } else {
        console.error('🔐 Error message:', error.message);
      }
      return false;
    }
  };

  const initializeAuth = useCallback(async () => {
    try {
      console.log('🔐 AuthContext - Initializing persistent authentication...');
      
      // Get stored tokens, user data, and expiry
      const [storedToken, storedRefreshToken, storedUserData, storedSession, storedExpiry] = await Promise.all([
        getSecureToken(),
        getSecureRefreshToken(),
        AsyncStorage.getItem(USER_KEY),
        AsyncStorage.getItem(SESSION_KEY),
        getTokenExpiry()
      ]);

      console.log('🔐 Stored token exists:', !!storedToken);
      console.log('🔐 Stored refresh token exists:', !!storedRefreshToken);
      console.log('🔐 Stored user data exists:', !!storedUserData);
      console.log('🔐 Stored session exists:', !!storedSession);
      console.log('🔐 Token expiry:', storedExpiry ? new Date(storedExpiry).toLocaleString() : 'Unknown');

      if (storedToken && storedUserData) {
        try {
          const userData = JSON.parse(storedUserData);
          let sessionData = null;
          
          if (storedSession) {
            sessionData = JSON.parse(storedSession);
          }
          
          // Set state
          setToken(storedToken);
          setUser(userData);
          setRefreshToken(storedRefreshToken);
          setSession(sessionData);
          setTokenExpiry(storedExpiry);
          
          // Set axios defaults
          axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
          
          console.log('🔐 Persistent authentication restored for:', userData.email);
          
          // Check if token needs immediate refresh
          if (storedExpiry && storedRefreshToken) {
            const timeUntilExpiry = storedExpiry - Date.now();
            if (timeUntilExpiry < TOKEN_EXPIRY_BUFFER) {
              console.log('🔐 Token expires soon, refreshing immediately...');
              await refreshAuthToken();
            } else {
              console.log(`🔐 Token valid for ${Math.round(timeUntilExpiry / 60000)} more minutes`);
            }
          } else if (storedRefreshToken) {
            console.log('🔐 No expiry info available, proactively refreshing token...');
            await refreshAuthToken();
          }
          
        } catch (parseError) {
          console.error('🔐 Error parsing stored data:', parseError);
          // Clear corrupted data
          await clearSecureTokens();
          await AsyncStorage.removeItem(USER_KEY);
        }
      } else {
        console.log('🔐 No stored authentication found');
      }
    } catch (error) {
      console.error('🔐 Error initializing auth:', error);
    } finally {
      setLoading(false);
    }
  }, [getSecureToken, getSecureRefreshToken, getTokenExpiry, refreshAuthToken, clearSecureTokens]);

  const login = useCallback(async (email, password) => {
    try {
      setLoading(true);
      console.log('🔐 Attempting login for:', email);

      const response = await axios.post(`${API_URL}/api/auth/login`, {
        email,
        password
      });

      if (response.data.access_token && response.data.user) {
        const { access_token, refresh_token, user: userData, session } = response.data;

        console.log('🔐 Login successful, storing tokens and user data');
        console.log('🔐 Has refresh token:', !!refresh_token);

        // Calculate token expiry (assume 1 hour lifespan)
        const expiry = Date.now() + 3540000; // 59 minutes

        // Store tokens securely with expiry
        await storeSecureToken(access_token, refresh_token, expiry);
        
        // Store user data and session
        await Promise.all([
          AsyncStorage.setItem(USER_KEY, JSON.stringify(userData)),
          session ? AsyncStorage.setItem(SESSION_KEY, JSON.stringify(session)) : Promise.resolve()
        ]);

        // Set axios defaults
        axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

        // Update state
        setToken(access_token);
        setUser(userData);
        setRefreshToken(refresh_token);
        setSession(session);
        setTokenExpiry(expiry);

        console.log('🔐 Persistent login successful for:', userData.email);
        console.log(`🔐 Token expires at: ${new Date(expiry).toLocaleString()}`);
        
        return { success: true };
      } else {
        console.error('🔐 Invalid login response');
        return { success: false, message: 'Invalid response from server' };
      }
    } catch (error) {
      console.error('🔐 Login error:', error);
      const message = error.response?.data?.error || error.message || 'Login failed';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
    }, [storeSecureToken]);

  const signup = useCallback(async (email, password, name) => {
    try {
      setLoading(true);
      console.log('🔐 Attempting signup for:', email);

      const response = await axios.post(`${API_URL}/api/auth/register`, {
        email,
        password,
        name
      });

      if (response.data.access_token && response.data.user) {
        const { access_token, refresh_token, user: userData, session } = response.data;
        const expiry = Date.now() + 3540000; // 59 minutes

        await storeSecureToken(access_token, refresh_token, expiry);
        await Promise.all([
          AsyncStorage.setItem(USER_KEY, JSON.stringify(userData)),
          session ? AsyncStorage.setItem(SESSION_KEY, JSON.stringify(session)) : Promise.resolve()
        ]);

        axios.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;
        setToken(access_token);
        setUser(userData);
        setRefreshToken(refresh_token);
        setSession(session);
        setTokenExpiry(expiry);

        console.log('🔐 Persistent signup successful for:', userData.email);
        return { success: true };
      } else {
        console.error('🔐 Invalid signup response');
        return { success: false, message: 'Invalid response from server' };
      }
    } catch (error) {
      console.error('🔐 Signup error:', error);
      const message = error.response?.data?.error || error.message || 'Signup failed';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  }, [storeSecureToken]);

  const logout = useCallback(async () => {
    try {
      console.log('🔐 Manual logout initiated');
      await clearSecureTokens();
      setUser(null);
      setToken(null);
      setRefreshToken(null);
      setSession(null);
      setTokenExpiry(null);
      delete axios.defaults.headers.common['Authorization'];
      console.log('🔐 Session cleared');
    } catch (error) {
      console.error('🔐 Logout error:', error);
    }
  }, [clearSecureTokens]);

  const refreshTokenManually = useCallback(async () => {
    console.log('🔄 Manually triggering token refresh...');
    return await refreshAuthToken();
  }, [refreshAuthToken]);

  const refreshTokenIfNeeded = useCallback(async () => {
    if (tokenExpiry) {
      const timeUntilExpiry = tokenExpiry - Date.now();
      if (timeUntilExpiry < TOKEN_EXPIRY_BUFFER) {
        console.log('🔄 Token is about to expire, refreshing now.');
        return await refreshAuthToken();
      }
      console.log('🔄 Token is still valid, no refresh needed.');
    }
    return true; // Return true if no refresh is needed
  }, [tokenExpiry, refreshAuthToken]);

  const isAuthenticated = !!token;

  const value = {
    user,
    token,
    refreshToken,
    session,
    tokenExpiry,
    loading,
    login,
    signup,
    logout,
    refreshTokenManually,
    refreshTokenIfNeeded,
    isAuthenticated
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
