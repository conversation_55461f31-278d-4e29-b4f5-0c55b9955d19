import React, { useEffect } from 'react';
import { View, StyleSheet, Animated, Text } from 'react-native';
import Svg, { G, Path, Circle } from 'react-native-svg';

const PieChart = ({ data, size = 200 }) => {
  const strokeWidth = size * 0.1; // 10% of size for stroke width
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const AnimatedCircle = Animated.createAnimatedComponent(Circle);
  const animatedValues = data.map(() => new Animated.Value(0));

  useEffect(() => {
    const animations = animatedValues.map(value =>
      Animated.timing(value, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: false,
      })
    );

    Animated.stagger(300, animations).start();
  }, []);

  const total = data.reduce((sum, item) => sum + item.value, 0);
  let startAngle = 0;

  return (
    <View style={styles.wrapper}>
      <View style={[styles.container, { width: size, height: size }]}>
        <Svg width={size} height={size}>
          <G rotation="-90" origin={`${size / 2}, ${size / 2}`}>
            {data.map((item, index) => {
              const percentage = item.value / total;
              const strokeDasharray = `${circumference * percentage} ${circumference * (1 - percentage)}`;
              const currentRotation = startAngle;
              startAngle += 360 * percentage;

              return (
                <AnimatedCircle
                  key={index}
                  cx={size / 2}
                  cy={size / 2}
                  r={radius}
                  stroke={item.color}
                  strokeWidth={strokeWidth}
                  strokeDasharray={strokeDasharray}
                  rotation={currentRotation}
                  origin={`${size / 2}, ${size / 2}`}
                  strokeLinecap="round"
                  fill="none"
                  strokeDashoffset={animatedValues[index].interpolate({
                    inputRange: [0, 1],
                    outputRange: [circumference, 0],
                  })}
                />
              );
            })}
          </G>
        </Svg>
        {/* Total value display in center */}
        <View style={styles.totalContainer}>
          <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
          <Text style={styles.totalLabel}>Total</Text>
        </View>
      </View>
      <View style={styles.legend}>
        {data.map((item, index) => (
          <View key={index} style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: item.color }]} />
            <Text style={styles.legendText}>{item.label}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  totalContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalLabel: {
    fontSize: 12,
    color: '#666',
  },
  legend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginTop: 15,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
    marginVertical: 5,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#666',
  },
});

export default PieChart;
