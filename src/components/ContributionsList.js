import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import contributionService from '../services/contributionService';

const ContributionsList = ({ contributions = [], loading = false, refreshTrigger, onError, maxItems = null }) => {
  const navigation = useNavigation();
  const [localContributions, setLocalContributions] = useState(contributions);
  const [localLoading, setLocalLoading] = useState(loading);
  const [refreshing, setRefreshing] = useState(false);

  const loadContributions = async (showLoading = true) => {
    try {
      if (showLoading) setLocalLoading(true);
      const data = await contributionService.getContributions();
      const formattedContributions = data.map(contrib => 
        contributionService.formatContribution(contrib)
      );
      setLocalContributions(formattedContributions);
    } catch (error) {
      console.error('Error loading contributions:', error);
      if (onError) {
        onError(error.message || 'Failed to load contributions');
      }
    } finally {
      if (showLoading) setLocalLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadContributions(false);
    setRefreshing(false);
  };

  const handleDeleteContribution = (contribution) => {
    Alert.alert(
      'Delete Contribution',
      `Are you sure you want to delete the contribution of ${contribution.formattedAmount}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await contributionService.deleteContribution(contribution.id);
              await loadContributions(false);
              Alert.alert('Success', 'Contribution deleted successfully');
            } catch (error) {
              console.error('Error deleting contribution:', error);
              Alert.alert('Error', error.message || 'Failed to delete contribution');
            }
          },
        },
      ]
    );
  };

  // Handle navigation to contribution details
  const handleContributionPress = (contributionId) => {
    navigation.navigate('ContributionDetail', { contributionId });
  };

  useEffect(() => {
    setLocalContributions(contributions);
  }, [contributions]);

  useEffect(() => {
    setLocalLoading(loading);
  }, [loading]);

  useEffect(() => {
    if (refreshTrigger) {
      loadContributions(false);
    }
  }, [refreshTrigger]);

  const renderContribution = (item) => (
    <TouchableOpacity 
      key={item.id} 
      style={styles.contributionItem}
      onPress={() => handleContributionPress(item.id)}
    >
      <View style={styles.contributionDetails}>
        <Text style={styles.contributionDate}>{item.formattedDate}</Text>
        <Text style={styles.contributionDesc}>{item.description || 'HSA Contribution'}</Text>
      </View>
      <View style={styles.contributionAmount}>
        <Text style={[styles.contributionValue, styles.positiveAmount]}>
          +{item.formattedAmount}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateText}>No contributions found</Text>
      <Text style={styles.emptyStateSubtext}>
        Add your first HSA contribution to track your progress
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading contributions...</Text>
      </View>
    );
  }

  // Get limited items if maxItems is specified
  const displayContributions = maxItems ? localContributions.slice(0, maxItems) : localContributions;

  return (
    <View style={styles.container}>
      {!displayContributions.length ? (
        renderEmptyState()
      ) : (
        <View>
          {displayContributions.map(renderContribution)}
          {maxItems && localContributions.length > maxItems && (
            <View style={styles.viewMoreIndicator}>
              <Text style={styles.viewMoreText}>
                View all contributions
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  contributionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contributionDetails: {
    flex: 1,
  },
  contributionDate: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 4,
  },
  contributionDesc: {
    fontSize: 16,
    color: '#333',
  },
  contributionAmount: {
    alignItems: 'flex-end',
  },
  contributionValue: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  positiveAmount: {
    color: '#43a047',
  },
  deleteButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  deleteButtonText: {
    color: '#e53935',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#666',
  },
  viewMoreIndicator: {
    paddingVertical: 12,
    marginTop: 8,
    alignItems: 'center',
  },
  viewMoreText: {
    color: '#4a6fa5',
    fontSize: 16,
    fontWeight: '500',
  },
  contributionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contributionDescription: {
    flex: 1,
    marginLeft: 10,
  },
  loader: {
    marginTop: 20,
  },
});

export default ContributionsList;
