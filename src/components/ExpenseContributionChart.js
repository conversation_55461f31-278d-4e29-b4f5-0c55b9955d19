import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import transactionService from '../services/transactionService';
import { forceRefreshAuthToken } from '../utils/authDebug';

/**
 * ExpenseContributionChart - A component that displays expense and contribution data in a bar chart
 * with navigation controls for switching between weeks and months.
 * 
 * @param {Object} props
 * @param {boolean} props.initialLoading - Initial loading state before component fetches its own data
 * @param {string} props.initialViewMode - Initial view mode ('week' or 'month')
 */
const ExpenseContributionChart = ({ initialLoading = false, initialViewMode = 'month' }) => {
  const screenWidth = Dimensions.get('window').width;
  const chartWidth = screenWidth - 60; // Full width minus padding
  const chartHeight = 200;
  
  // [Rest of the component code remains exactly the same]
  // ... existing code ...
};

export default ExpenseContributionChart;
