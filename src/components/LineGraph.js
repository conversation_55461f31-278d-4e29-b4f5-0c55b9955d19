import React, { useMemo } from 'react';
import { View } from 'react-native';
import { Graph } from 'react-native-graph';

const LineGraph = ({ points, color }) => {
  if (!points || points.length === 0) {
    return <View style={{ height: 200 }} />;
  }
  const graphData = useMemo(() => {
    if (!points || points.length === 0) return [];
    return points.map(point => ({
      date: point.timestamp,
      value: point.value
    }));
  }, [points]);

  return (
    <View style={{ height: 200 }}>
      <Graph
        style={{ flex: 1 }}
        data={graphData}
        animated={true}
        color={color}
        enablePanGesture={true}
        selection={{
          enabled: true,
          color: color,
          textColor: '#333333',
          formatter: (value) => `$${value.toFixed(2)}`
        }}
        range={{
          min: Math.min(...graphData.map(p => p.value)) * 0.95,
          max: Math.max(...graphData.map(p => p.value)) * 1.05
        }}
      />
    </View>
  );
};

export default LineGraph;
