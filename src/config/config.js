import { Platform } from 'react-native';

// API Configuration
const config = {
// Use the appropriate API base URL based on platform
//daniels place
API_BASE_URL: Platform.OS === 'android' ? 'http://10.0.2.2:8088/api' : 'http://127.0.0.1:8088/api',

// API_BASE_URL: Platform.OS === 'android' ? 'http://10.0.2.2:8088/api' : 'http://127.0.0.1:8088/api',
// Authentication Configuration
AUTH: {
// Never expire tokens - persistent login
PERSISTENT_SESSION: true,
// No automatic token refresh needed - we handle 401s gracefully
AUTO_REFRESH: false,
// Storage keys (consistent with AuthContext)
TOKEN_STORAGE_KEY: 'auth_token',
USER_STORAGE_KEY: 'user_data',
},
// Legacy settings (kept for backward compatibility)
TOKEN_REFRESH_INTERVAL: 365 * 24 * 60 * 60 * 1000, // 1 year
MAX_TOKEN_AGE: 365 * 24 * 60 * 60 * 1000, // 1 year
};

export default config;