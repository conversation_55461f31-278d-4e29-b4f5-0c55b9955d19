---
description: Push to GitHub with AI-Generated Commit Messages
---

Push to GitHub with AI-Generated Commit Messages (Cascade)
==========================================================

1. Initialize Git Repository in Windsurf
----------------------------------------
- Run:
    git status

2. Understand what you are pushing out 
---------------------------------------
- Create a commit message based on diffs

3. Review & Commit
------------------
- Review and edit the generated commit message if needed.
- Commit your changes:
      git commit -m "[generated-message]"

4. Push to GitHub
-----------------
- Push your commit to the main branch:
      git push -u origin main