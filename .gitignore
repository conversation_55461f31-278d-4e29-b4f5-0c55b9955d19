# React Native / JavaScript
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp/
.pnp.js
coverage/
build/
dist/
.expo/
web-build/
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Flask / Python
/backend/venv/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
instance/
.webassets-cache
.coverage
htmlcov/
.pytest_cache/
.tox/
.coverage.*
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# IDE / Editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
Thumbs.db

# iOS
ios/Pods/
ios/build/
ios/Podfile.lock
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android
android/app/build/
android/build/
android/gradle/
android/local.properties
android/.gradle
android/.idea
android/*.iml
android/.settings
android/.project
*.apk
*.aab
output.json
